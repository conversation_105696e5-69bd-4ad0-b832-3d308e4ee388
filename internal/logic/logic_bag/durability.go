package logic_bag

import (
	"context"
	"fmt"
	"hallsrv/internal/dao/dao_trip_rod"
	"hallsrv/internal/logic/logic_durable"
	logicItem "hallsrv/internal/logic/logic_item"
	logicNotify "hallsrv/internal/logic/logic_notify"
	"hallsrv/internal/model/model_bag"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"github.com/ldy105cn/xorm"
)

// 维修钓组
func FixRodDurability(ctx context.Context, playerId uint64, rigId int32, sitIds []int32) (*commonPB.Reward, *model_bag.TTripRodGroup, error) {
	entry := logx.NewLogEntry(ctx)
	// 取对应杆组配置
	rodInfo, err := dao_trip_rod.Get(ctx, playerId, rigId)
	if err != nil {
		return nil, nil, err
	}
	lossItemList := make([]*commonPB.ItemBase, 0)

	for _, sitId := range sitIds {
		rodItem := rodInfo.Data[sitId]
		if rodItem == nil {
			entry.Errorf("rodItem is nil rid:%+v sitId:%+v", rigId, sitId)
			return nil, nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("rodItem[%+v:%+v] is nil", rigId, sitId))
		}
		rodItemInfo := rodItem.ToItem(ctx)
		lossItem, err := logic_durable.DoFixItem(ctx, rodItemInfo)
		if err != nil {
			return nil, nil, err
		}
		// 维修失败爆杆
		if item_kit.GetMaxDurability(ctx, rodItemInfo) <= 0 {
			// rodItem.ItemId = 0
			rodItem.InstanceId = ""
		}
		// 替换更新属性
		rodItem.Args = rodItemInfo.Extra
		lossItemList = append(lossItemList, lossItem...)
	}

	var reward *commonPB.Reward
	if len(lossItemList) > 0 {
		reward, err = logicItem.OperatePlayerItem(ctx, playerId, lossItemList, commonPB.ITEM_OPERATION_IO_REDUCE, commonPB.ITEM_SOURCE_TYPE_IST_FIX_ITEM, commonPB.STORAGE_TYPE_ST_STORE, false)
		if err != nil {
			entry.Errorf("item fix loss fail:%+v items:%+v", err, lossItemList)
			return nil, nil, err
		}
	}

	// 检查维修状态
	engine, err := dao_trip_rod.GetSqlEngine()
	if err != nil {
		entry.Errorf("sql engine fail:%+v", err)
		return nil, nil, err
	}
	_, err = engine.Transaction(func(s *xorm.Session) (interface{}, error) {
		err = dao_trip_rod.Update(ctx, s, rodInfo)
		if err != nil {
			return nil, err
		}
		return nil, nil

	})

	if err != nil {
		entry.Errorf("fix rig:%+v list:%+v fail:%+v", rigId, sitIds, err)
		return nil, nil, err
	}

	logicNotify.PlayerRodRigNotify(ctx, playerId, rodInfo)

	return reward, rodInfo, nil
}

// 扣除钓组耐久度信息
func LossRodDurability(ctx context.Context, playerId uint64, ridId int32, kv map[int32]int64) error {
	entry := logx.NewLogEntry(ctx)

	// 取对应杆组配置
	rodInfo, err := dao_trip_rod.Get(ctx, playerId, ridId)
	if err != nil {
		return err
	}

	// 扣除耐久不可出现负数
	for _, v := range kv {
		if v < 0 {
			return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "loss durability less than zero")
		}
	}

	// 开始尝试扣除耐久度
	for k, v := range kv {
		err = lossRodItemDurability(ctx, rodInfo, k, v)
		if err != nil {
			entry.Warnf("loss rod item durability error: %v", err)
			return err
		}
	}

	// 更新数据库数据
	engine, err := dao_trip_rod.GetSqlEngine()
	if err != nil {
		entry.Errorf("sql engine fail:%+v", err)
		return err
	}
	_, err = engine.Transaction(func(session *xorm.Session) (interface{}, error) {
		err = dao_trip_rod.Update(ctx, session, rodInfo)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		entry.Errorf("failed to update trip rod for err:%+v", err)
		return err
	}

	logicNotify.PlayerRodRigNotify(ctx, playerId, rodInfo)

	return nil
}

// 扣除道具耐久
func lossRodItemDurability(ctx context.Context, group *model_bag.TTripRodGroup, sitId int32, lossVal int64) error {
	data, ok := group.Data[sitId]
	if !ok {
		return protox.CodeError(commonPB.ErrCode_ERR_NOT_EXIST, fmt.Sprintf("rod:[%+v:%+v] not exist", group.Id, sitId))
	}
	if data.InstanceId == "" {
		return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, fmt.Sprintf("rod:[%+v:%+v] not have instance", group.Id, sitId))

	}
	item := data.ToItem(ctx)
	// 补充修复程序
	switch item.ItemType {
	case commonPB.ITEM_TYPE_IT_TACKLE_LURES, commonPB.ITEM_TYPE_IT_TACKLE_BAIT, commonPB.ITEM_TYPE_IT_TACKLE_HOOKS:
		if item.Extra[int32(commonPB.ITEM_EXTRA_KEY_IEK_MAX_DURABILITY)] == 0 {
			item_kit.SetMaxDurability(ctx, item, 10000)
			item_kit.SetCurrDurability(ctx, item, 10000)
		}
	}

	// 扣除耐久
	val := item_kit.GetCurrDurability(ctx, item)

	if val <= lossVal || val <= 0 {
		item_kit.SetCurrDurability(ctx, item, 0)
		data.InstanceId = ""
	} else {
		item_kit.SetCurrDurability(ctx, item, val-lossVal)
	}
	// 重新提取赋值
	data.Args = item.Extra

	return nil
}
