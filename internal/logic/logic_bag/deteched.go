package logic_bag

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/dao/dao_trip_rod"
	logicItem "hallsrv/internal/logic/logic_item"
	logicNotify "hallsrv/internal/logic/logic_notify"
	logicRig "hallsrv/internal/logic/logic_rig"
	"hallsrv/internal/model/model_bag"
	modelRodRig "hallsrv/internal/model/model_rod_rig"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
)

// detached
// LoadRodBag 加载鱼竿
func LoadRodBag(ctx context.Context, playerId uint64, rigId int32, id int32) (info *commonPB.RodBagInfo, err error) {
	entry := logx.NewLogEntry(ctx)
	ridList, err := logicRig.QueryPlayerRodRigInfo(ctx, playerId)
	if err != nil {
		return nil, err
	}

	var target *modelRodRig.TRodRigInfo
	for _, ridGroup := range ridList {
		if ridGroup.RigId == rigId {
			target = ridGroup
			break
		}
	}
	if target == nil {
		return nil, fmt.Errorf("rigId %v set not found", rigId)
	}

	// 判断是否存在数据
	_, err = dao_trip_rod.Get(ctx, playerId, id)
	if err == nil {
		return nil, fmt.Errorf("id %v already exist", id)
	}
	if !errors.Is(err, redisx.Empty) {
		return nil, err
	}

	// 上分布式锁
	unlock := dlm.LockKey(rodBagDlmKey(playerId))
	defer unlock()

	new := &model_bag.TTripRodGroup{}
	new.Id = id // 必须先赋值id

	// 构建新的塞进去
	new.FromRigInfo(target)

	targetIds := make([]int64, 0)
	for _, rodItem := range new.ToRodList() {
		if rodItem.ItemId == -1 || rodItem.ItemId == 0 {
			continue
		}
		targetIds = append(targetIds, rodItem.ItemId)
	}

	storeItems, err := logicItem.PlayerQueryItemInfoByIdList(ctx, playerId, targetIds)
	if err != nil {
		return nil, err
	}

	bagItems, err := GetTripBagItem(ctx, playerId, commonPB.TRIP_BAG_TYPE_TBT_FISHING_GEAR)
	if err != nil {
		return nil, err
	}
	delStoreItems := make([]*commonPB.OriginLoot, 0)
	delBagItems := make([]*commonPB.OriginLoot, 0)
	for _, rodInfo := range new.Data {
		delItem, bagType := selectRodItem(storeItems, bagItems, rodInfo.ItemId, rodInfo.InstanceId)
		if delItem == nil {
			continue
		}
		if bagType == commonPB.STORAGE_TYPE_ST_STORE {
			delStoreItems = append(delStoreItems, delItem)
		} else {
			delBagItems = append(delBagItems, delItem)
		}
		rodInfo.FromItem(delItem.Item)
	}

	engine, err := dao_trip_rod.GetSqlEngine()
	if err != nil {
		return nil, err
	}

	session := engine.NewSession()
	defer session.Close()

	//  2pc
	session.Begin()

	// 更新
	err = dao_trip_rod.Update(ctx, session, new)
	if err != nil {
		errRollback := session.Rollback()
		if errRollback != nil {
			return nil, errRollback
		}
		return nil, err
	}

	bagUpdateList := make([]*commonPB.ItemInfo, 0)
	// 扣背包
	if len(delBagItems) > 0 {
		reward, err := logicItem.OperateItemByInstance(ctx, playerId, delBagItems, commonPB.ITEM_OPERATION_IO_REDUCE, commonPB.STORAGE_TYPE_ST_BAG, commonPB.ITEM_SOURCE_TYPE_IST_BAG_MOVE)
		_ = reward

		if err != nil {
			entry.Errorf("del item fail:%+v", err)
			errRollBack := session.Rollback()
			if errRollBack != nil {
				return nil, errRollBack
			}
			return nil, err
		}
		bagUpdateList = append(bagUpdateList, reward.GetItemList()...)
		entry.Infof("del bag item success:%v", delBagItems)
	}

	// 扣仓库
	if len(delStoreItems) > 0 {
		reward, err := logicItem.OperateItemByInstance(ctx, playerId, delStoreItems, commonPB.ITEM_OPERATION_IO_REDUCE, commonPB.STORAGE_TYPE_ST_STORE, commonPB.ITEM_SOURCE_TYPE_IST_BAG_MOVE)
		_ = reward
		if err != nil {
			entry.Errorf("del item fail:%+v", err)
			errRollBack := session.Rollback()
			if errRollBack != nil {
				return nil, errRollBack
			}
			return nil, err
		}
		entry.Infof("del store item success:%v", delStoreItems)
	}

	session.Commit()

	// 背包更新通知
	rewardUpdInfo := &commonPB.Reward{
		ItemList: bagUpdateList,
	}
	logicNotify.PlayerRewardInfoNotify(ctx, playerId, commonPB.STORAGE_TYPE_ST_BAG, rewardUpdInfo)

	logicNotify.PlayerRodRigNotify(ctx, playerId, new)

	return new.ToProto(ctx), nil
}

// detached
// PutRodBag 放入背包
func PutRodBag(ctx context.Context, playerId uint64, id int32, bagIndex int32) (info *commonPB.RodBagInfo, err error) {

	// 判断是否存在数据
	rig, err := dao_trip_rod.Get(ctx, playerId, id)
	if err != nil {
		return nil, err
	}
	cfg := cmodel.GetItemConst(consul_config.WithGrpcCtx(ctx))
	maxRodIndex := int32(cfg.TripBagRodRigCells)
	if bagIndex > maxRodIndex {
		return nil, fmt.Errorf("bagIndex %v out of range", bagIndex)
	}

	// 上分布式锁
	unlock := dlm.LockKey(rodBagDlmKey(playerId))
	defer unlock()

	engine, err := dao_trip_rod.GetSqlEngine()
	if err != nil {
		return nil, err
	}

	session := engine.NewSession()
	defer session.Close()

	//  2pc
	session.Begin()

	// 更新
	oldRigId, err := dao_trip_rod.Put(ctx, session, playerId, id, bagIndex)
	if err != nil {
		errRollback := session.Rollback()
		if errRollback != nil {
			return nil, errRollback
		}
		return nil, err
	}

	session.Commit()
	rig.BagIndex = bagIndex

	oldRig := &model_bag.TTripRodGroup{}
	if oldRigId != 0 {
		oldRig, err = dao_trip_rod.Get(ctx, playerId, oldRigId)
		if err != nil {
			return nil, err
		}
	}

	logicNotify.PlayerRodRigNotify(ctx, playerId, oldRig, rig)
	return rig.ToProto(ctx), nil
}

// detached
// DelRodBag 卸下背包杆组
func DelRodBag(ctx context.Context, playerId uint64, id int32) (info *commonPB.RodBagInfo, err error) {
	info = &commonPB.RodBagInfo{}

	engine, err := dao_trip_rod.GetSqlEngine()
	if err != nil {
		return
	}

	// 上分布式锁
	unlock := dlm.LockKey(rodBagDlmKey(playerId))
	defer unlock()

	session := engine.NewSession()
	defer session.Close()

	session.Begin()

	// 2pc
	// 卸下
	err = dao_trip_rod.Unload(ctx, session, playerId, id)
	if err != nil {
		errRollBack := session.Rollback()
		if errRollBack != nil {
			return info, errRollBack
		}
		return
	}

	session.Commit()

	rig, err := dao_trip_rod.Get(ctx, playerId, id)
	if err != nil {
		return nil, err
	}

	logicNotify.PlayerRodRigNotify(ctx, playerId, rig)
	return rig.ToProto(ctx), nil
}


// detached
// SaveRodStore 存入仓库
func SaveRodStore(ctx context.Context, playerId uint64, rigId int32) (info *commonPB.RodBagInfo, err error) {
	entry := logx.NewLogEntry(ctx)
	// 获取鱼竿方案
	ridList, err := logicRig.QueryPlayerRodRigInfo(ctx, playerId)
	if err != nil {
		return nil, err
	}

	var target *modelRodRig.TRodRigInfo
	for _, ridGroup := range ridList {
		if ridGroup.RigId == rigId {
			target = ridGroup
			break
		}
	}
	if target == nil {
		return nil, fmt.Errorf("rigId %v set not found", rigId)
	}

	// 上分布式锁
	unlock := dlm.LockKey(rodBagDlmKey(playerId))
	defer unlock()

	newRig := &model_bag.TTripRodGroup{}

	// 构建新的塞进去
	newRig.FromRigInfo(target)

	targetIds := make([]int64, 0)
	for _, rodItem := range newRig.ToRodList() {
		if rodItem.ItemId == -1 || rodItem.ItemId == 0 {
			continue
		}
		targetIds = append(targetIds, rodItem.ItemId)
	}

	storeItems, err := logicItem.PlayerQueryItemInfoByIdList(ctx, playerId, targetIds)
	if err != nil {
		return nil, err
	}

	delStoreItems := make([]*commonPB.OriginLoot, 0)
	for _, rodInfo := range newRig.Data {
		if rodInfo.ItemId == -1 || rodInfo.ItemId == 0 {
			continue
		}
		delItem, bagType := selectRodItemByStore(storeItems, rodInfo.ItemId, rodInfo.InstanceId)
		if delItem == nil {
			return nil, fmt.Errorf("rigId %d missing %d parts", rigId, rodInfo.ItemId)
		}
		if bagType == commonPB.STORAGE_TYPE_ST_STORE {
			delStoreItems = append(delStoreItems, delItem)
		}
		rodInfo.FromItem(delItem.Item)
	}

	engine, err := dao_trip_rod.GetSqlEngine()
	if err != nil {
		return nil, err
	}

	session := engine.NewSession()
	defer session.Close()

	//  2pc
	session.Begin()

	// 更新
	err = dao_trip_rod.Insert(ctx, session, newRig)
	entry.Infof("insert rod group:%+v", newRig)
	if err != nil {
		errRollback := session.Rollback()
		if errRollback != nil {
			return nil, errRollback
		}
		return nil, err
	}

	// 扣仓库
	if len(delStoreItems) > 0 {
		reward, err := logicItem.OperateItemByInstance(ctx, playerId, delStoreItems, commonPB.ITEM_OPERATION_IO_REDUCE, commonPB.STORAGE_TYPE_ST_STORE, commonPB.ITEM_SOURCE_TYPE_IST_BAG_MOVE)
		_ = reward
		if err != nil {
			entry.Errorf("del item fail:%+v", err)
			errRollBack := session.Rollback()
			if errRollBack != nil {
				return nil, errRollBack
			}
			return nil, err
		}
		entry.Infof("del store item success:%v", delStoreItems)
	}

	session.Commit()

	logicNotify.PlayerRodRigNotify(ctx, playerId, newRig)

	return newRig.ToProto(ctx), nil
}

// 筛选上阵道具从仓库
func selectRodItemByStore(storeItems []*commonPB.ItemInfo, itemId int64, instanceId string) (DelItems *commonPB.OriginLoot, storage commonPB.STORAGE_TYPE) {

	// 暂时不考虑同一套装备能重复装相同的道具
	// 检查仓库是否有满足条件道具

	item := getRodItem(storeItems, itemId, instanceId)
	if item != nil {
		return &commonPB.OriginLoot{Item: item, Value: 1}, commonPB.STORAGE_TYPE_ST_STORE
	}

	return nil, commonPB.STORAGE_TYPE_ST_UNKNOWN
}