package logic_durable

import (
	"context"
	"fmt"
	logicItem "hallsrv/internal/logic/logic_item"
	rpcAsset "hallsrv/internal/repo/rpc_asset"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/mathx"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
)

// 耐久度相关计算

// FixStorageItem 修复存储道具
func FixStorageItem(ctx context.Context, playerId uint64, itemInstanceIds []string, bagType commonPB.STORAGE_TYPE) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)
	mIds := make(map[string]bool)
	for _, id := range itemInstanceIds {
		mIds[id] = true
	}

	// TODO 针对性查询

	// 找到对应的道具
	itemList, err := rpcAsset.GetPlayerCategoryListItemInfo(ctx, playerId, interceptor.GetRPCOptions(ctx).ProductId, []commonPB.ITEM_CATEGORY{commonPB.ITEM_CATEGORY_IC_TACKLE}, bagType)
	if err != nil {
		return nil, err
	}

	newItemList := make([]*commonPB.OriginLoot, 0)
	lossItemList := make([]*commonPB.ItemBase, 0)

	// 针对每个道具计算消耗 / 和修改
	for _, item := range itemList {
		if _, ok := mIds[item.Item.GetInstanceId()]; ok {
			loss, err := DoFixItem(ctx, item.GetItem())
			if err != nil {
				return nil, err
			}
			// 检查耐久度上限是否为0
			maxDurability := item_kit.GetMaxDurability(ctx, item.GetItem())
			if maxDurability <= 0 {
				// 删除道具
				loss = append(loss, &commonPB.ItemBase{ItemId: item.GetItem().GetItemId(), ItemCount: 1, InstanceId: item.GetItem().GetInstanceId()})
			} else {
				// 更新道具状态
				newItemList = append(newItemList, &commonPB.OriginLoot{Item: item.GetItem(), Value: 1})
			}

			lossItemList = append(lossItemList, loss...)
		}
	}

	var reward *commonPB.Reward
	// 扣除维修货币
	if len(lossItemList) > 0 {
		reward, err = logicItem.OperatePlayerItem(ctx, playerId, lossItemList, commonPB.ITEM_OPERATION_IO_REDUCE, commonPB.ITEM_SOURCE_TYPE_IST_FIX_ITEM, commonPB.STORAGE_TYPE_ST_STORE, false)
		if err != nil {
			entry.Errorf("item fix loss fail:%+v items:%+v", err, lossItemList)
			return nil, err
		}
	}

	if len(newItemList) > 0 {
		_, err = logicItem.OperateItemByInstance(ctx, playerId, newItemList, commonPB.ITEM_OPERATION_IO_UPDATE, bagType, commonPB.ITEM_SOURCE_TYPE_IST_FIX_ITEM)
		if err != nil {
			entry.Errorf("item fix update fail:%+v items:%+v", err, newItemList)
			return nil, err
		}
	}

	return reward, nil
}

func DoFixItem(ctx context.Context, item *commonPB.Item) (loss []*commonPB.ItemBase, err error) {
	entry := logx.NewLogEntry(ctx)
	// 杆组已磨损 不需要维修
	if item.InstanceId == "" {
		return []*commonPB.ItemBase{}, nil
	}

	itemCfg := cmodel.GetItem(item.GetItemId(), consul_config.WithGrpcCtx(ctx))
	if itemCfg == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("unknown itemCfg: %d", item.GetItemId()))
	}
	price := itemCfg.Price

	// 剩余耐久百分比 = 当前耐久 / 当前耐久上限
	max := item_kit.GetMaxDurability(ctx, item)
	curr := item_kit.GetCurrDurability(ctx, item)
	if max <= 0 {
		entry.Errorf("item max durability <= 0, item: %v", item)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, fmt.Sprintf("max less than zero itemId:%d instanceId:%+v", item.GetItemId(), item.GetInstanceId()))
	}
	var racer int64 = 100                          //倍率乘数
	remainPercent := int64(racer - curr*racer/max) // 处理出整数
	lossVal := remainPercent * price / 100

	// 道具消耗
	loss = []*commonPB.ItemBase{}
	if lossVal > 0 {
		loss = append(loss, &commonPB.ItemBase{
			ItemId:    int64(commonPB.ITEM_TYPE_IT_CURRENCY_COIN),
			ItemCount: lossVal,
		})
	}

	// 更新耐久属性
	// 1. 玩家进行普通维修时，最大耐久有概率减少
	// 2. 概率：品级系数*等级系数/10*调整系数/100，品级系数为：1+品级编号/10，例如一个S级30级的钓具，（1+4/10）*30/10*1/100=4.2%
	// 3. 耐久下降比例：10%，例如：一个S级30级的钓具，耐久上限是5000，维修后，出发耐久下降，下降后耐久上限是4500，前台展示为：90%
	// 4. 普通维修时，最大耐久只会下降或持平，不会增长

	itemCost := cmodel.GetItemConst(consul_config.WithGrpcCtx(ctx))
	// 调整系数 保底数值
	adjustFactor := float32(10000)
	if itemCost.MaintainMaxReduceFactor != 0 {
		adjustFactor = float32(itemCost.MaintainMaxReduceFactor)
	}
	adjustFactor /= 10000
	adjustFactor /= 100
	qualityFactor := float32(1+itemCfg.Quality) / 10
	levelFactor := float32(itemCfg.UnlockLevel) / 10

	// 垃圾浮点数 0.1 * 0.1 * 0.01 = 0.000100000005
	reduceFactor := qualityFactor * levelFactor * adjustFactor
	reduceFactorInt := int32(reduceFactor * 10000) // 取4位小数

	weight := random.Int32n(10000)
	// 执行磨损
	if weight <= reduceFactorInt {
		tackleCfg, err := item_kit.GetTackleConf(ctx, item.ItemId)
		if err != nil {
			return nil, err
		}
		// 新最大上限 = 当前最大上限 - （配置最大上限 * 磨损比例）
		reduceMax := int64(tackleCfg.Durability*(itemCost.MaintainReduceFactor)) / 10000
		newMax := max - reduceMax
		newMax = mathx.Max(newMax, 0)
		item.Extra[int32(commonPB.ITEM_EXTRA_KEY_IEK_MAX_DURABILITY)] = newMax
	}
	entry.Debugf("item fix loss max reduceFactor: %+v, weight: %+v", reduceFactorInt, weight)
	// 获取最新上限
	max = item.Extra[int32(commonPB.ITEM_EXTRA_KEY_IEK_MAX_DURABILITY)]
	item.Extra[int32(commonPB.ITEM_EXTRA_KEY_IEK_CURR_DURABILITY)] = max

	return loss, nil
}
