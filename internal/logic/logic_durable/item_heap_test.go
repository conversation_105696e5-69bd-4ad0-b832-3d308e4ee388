package logic_durable

import (
	"context"
	test_init "hallsrv/internal/test"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func TestQueryPlayerItemHeapDurable(t *testing.T) {

	test_init.InitRedisConsul()
	test_init.InitSql()
	ctx := context.Background()
	playerId := uint64(1)
	itemId := int64(1)

	// 更新
	// UpdatePlayerItemHeapDurable(ctx, playerId, itemId, 10)
	// UpdatePlayerItemHeapDurable(ctx, playerId, 2, 10)

	// 查询
	durable := QueryPlayerItemHeapDurable(ctx, playerId, itemId)
	durableArr, _ := QueryPlayerAllItemHeapDurable(ctx, playerId)

	t.Logf("durable: %+v, durableArr:%+v", durable, durableArr)
}

func TestLossItemHeap(t *testing.T) {
	test_init.Init()
	playerId := uint64(9000079)
	ctx := test_init.NewCtxWithPlayerId(playerId)
	err :=LossItemHeap(ctx, playerId, 309201001, 100, commonPB.ITEM_SOURCE_TYPE_IST_ACTIVITY_CONTINUOUS_LOGIN)
	if err!= nil {
		t.Fatalf("err: %+v", err)
	}
}
