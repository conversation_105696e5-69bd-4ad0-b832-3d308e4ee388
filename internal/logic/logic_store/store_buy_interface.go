package logicStore

import (
	"context"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	daoGoods "hallsrv/internal/dao/dao_goods"
	"hallsrv/internal/logic/logic_player_state"
	modelPlayer "hallsrv/internal/model/model_player"
	modelStore "hallsrv/internal/model/model_store"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/algorithm/alg_bitmap"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

type StoreBuyInterface interface {
	BuyGoods(ctx context.Context, playerId uint64, storeStyle commonPB.STORE_SHOW_STYLE, storeBuyId int64, buyCount int32) (*commonPB.Reward, error)
}

func BuyStoreGoods(ctx context.Context, playerId uint64, storeStyle commonPB.STORE_SHOW_STYLE, storeBuyId int64, buyCount int32) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)

	// 商品详细信息
	goodsInfo, err := modelStore.NewGoodsInfo(ctx, storeBuyId, storeStyle)
	if goodsInfo == nil || goodsInfo.GoodsConf == nil || err != nil {
		entry.Errorf("player:%d buy store id:%d err:%+v", playerId, storeBuyId, err)
		return nil, err
	}

	// 房间商品 需要校验背包空间是否足够
	if storeStyle == commonPB.STORE_SHOW_STYLE_STRT_ROOM {
		if !CheckStoreRoomBagSpace(ctx, playerId, goodsInfo.GoodsConf, buyCount) {
			entry.Warnf("player:%d buy store room bag space is not enough", playerId)
			return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_BAG_FULL, "store room bag space is not enough")
		}
	}

	var buyInterface StoreBuyInterface

	switch goodsInfo.GoodsConf.LimitType {
	case int32(commonPB.BUY_LIMIT_TYPE_BLT_DAY),
		int32(commonPB.BUY_LIMIT_TYPE_BLT_WEEK),
		int32(commonPB.BUY_LIMIT_TYPE_BLT_MONTH):
		buyInterface = new(BuyTimeLimit)
	case int32(commonPB.BUY_LIMIT_TYPE_BLT_FOREVER):
		buyInterface = new(BuyPermanent)
	default:
		buyInterface = new(BuyUnlimit)
	}

	return buyInterface.BuyGoods(ctx, playerId, storeStyle, storeBuyId, buyCount)
}

// 不限制的商品
type BuyUnlimit struct {
}

func (b *BuyUnlimit) BuyGoods(ctx context.Context, playerId uint64, storeStyle commonPB.STORE_SHOW_STYLE, storeBuyId int64, buyCount int32) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)
	// 购买流程
	rewardInfo, err := PlayerBuyStoreGoods(ctx, playerId, storeStyle, storeBuyId, buyCount)
	if rewardInfo == nil || err != nil {
		entry.Errorf("player:%d buy store goods error, id:%d, err:%+v", playerId, storeBuyId, err)
		return nil, err
	}

	return rewardInfo, nil
}

// 时间限制的商品 (如日限制 周限制 等)
type BuyTimeLimit struct {
}

func (b *BuyTimeLimit) BuyGoods(ctx context.Context, playerId uint64, storeStyle commonPB.STORE_SHOW_STYLE, storeBuyId int64, buyCount int32) (*commonPB.Reward, error) {
	// 商城购买配置
	entry := logx.NewLogEntry(ctx)
	storeBuyConf := cmodel.GetStoreBuy(storeBuyId, consulconfig.WithGrpcCtx(ctx))

	goodsInfo, err := modelStore.NewGoodsInfo(ctx, storeBuyId, storeStyle)
	if goodsInfo == nil || goodsInfo.GoodsConf == nil || err != nil {
		entry.Errorf("player:%d buy store id:%d err:%+v", playerId, storeBuyId, err)
		return nil, err
	}

	// 查询商品信息
	buyInfo, err := daoGoods.QueryPlayerGoodsBuyInfo(ctx, playerId, storeBuyConf.GoodsId)
	if err != nil {
		entry.Errorf("get player buy info error, player:%d goods:%d", playerId, storeBuyConf.GoodsId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, "query buy limit info error")
	}

	buyInfo.CalTimes(goodsInfo.GoodsConf.LimitType)
	if goodsInfo.GoodsConf.LimitCount <= buyInfo.BuyTimes {
		entry.Warnf("player:%d buy store goods, limit count is over", playerId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_GOODS_LIMIT, "buy times limit")
	}

	// 购买流程
	rewardInfo, err := PlayerBuyStoreGoods(ctx, playerId, storeStyle, storeBuyId, buyCount)
	if rewardInfo == nil || err != nil {
		entry.Errorf("player:%d buy store goods error, id:%d, err:%+v", playerId, storeBuyId, err)
		return nil, err
	}

	buyInfo.BuyTimes += 1
	buyInfo.LastBuyTime = timex.Now().Unix()
	if err := daoGoods.UpdatePlayerGoodsBuyInfo(ctx, playerId, buyInfo); err != nil {
		entry.Errorf("update player buy info error, player:%d goods:%d, err:%v", playerId, storeBuyConf.GoodsId, err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, "update buy limit info error")
	}

	return rewardInfo, nil
}

// 永久限制的商品
type BuyPermanent struct {
}

func (b *BuyPermanent) BuyGoods(ctx context.Context, playerId uint64, storeStyle commonPB.STORE_SHOW_STYLE, storeBuyId int64, buyCount int32) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)

	// 商品详细信息
	goodsInfo, err := modelStore.NewGoodsInfo(ctx, storeBuyId, storeStyle)
	if goodsInfo == nil || goodsInfo.GoodsConf == nil || err != nil {
		entry.Errorf("player:%d buy store id:%d err:%+v", playerId, storeBuyId, err)
		return nil, err
	}

	// 永久限制信息
	buyGoodsInfo, err := logic_player_state.QueryPlayerStateBmStr(ctx, playerId, modelPlayer.GetPlayerStateBmGoodsRdsField())
	if err != nil {
		entry.Errorf("get player buy info error, player:%d goods:%d", playerId, goodsInfo.GoodsId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, "query buy limit info error")
	}

	// 是否已经购买
	isExistBuy := alg_bitmap.InBitmap(buyGoodsInfo, uint64(goodsInfo.GoodsId))
	if isExistBuy {
		entry.Warnf("player:%d buy store goods, already exist", playerId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_GOODS_LIMIT, "exist buy")
	}

	// 购买流程
	rewardInfo, err := PlayerBuyStoreGoods(ctx, playerId, storeStyle, storeBuyId, buyCount)
	if rewardInfo == nil || err != nil {
		entry.Errorf("player:%d buy store goods error, id:%d, err:%+v", playerId, storeBuyId, err)
		return nil, err
	}

	// 添加购买信息
	curBuyInfo, err := alg_bitmap.AddBit(buyGoodsInfo, uint64(goodsInfo.GoodsId))
	if err != nil {
		entry.Errorf("set bitmap error, player:%d goods:%d", playerId, goodsInfo.GoodsId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, "set buy limit info error")
	}

	// 更新购买信息
	if err := logic_player_state.UpdatePlayerStateBmStr(ctx, playerId, modelPlayer.GetPlayerStateBmGoodsRdsField(), curBuyInfo); err != nil {
		entry.Errorf("update player buy info error, player:%d goods:%d", playerId, goodsInfo.GoodsId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, "update buy limit info error")
	}

	return rewardInfo, nil
}
