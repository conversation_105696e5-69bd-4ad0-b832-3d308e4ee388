package logicItem

import (
	"context"
	"fmt"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	daoItemCd "hallsrv/internal/dao/dao_item_cd"
	"hallsrv/internal/logic/logic_item/item_dispose"
	logicNotify "hallsrv/internal/logic/logic_notify"
	logicRole "hallsrv/internal/logic/logic_role"
	modelItem "hallsrv/internal/model/model_item"
	"hallsrv/internal/pubsub/publish"
	rpcAsset "hallsrv/internal/repo/rpc_asset"
)

// PlayerQueryItemInfoByIdList 按道具id列表查询玩家道具信息
func PlayerQueryItemInfoByIdList(ctx context.Context, playerId uint64, itemIds []int64) ([]*commonPB.ItemInfo, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || len(itemIds) <= 0 {
		return nil, fmt.Errorf("playerId:%d or itemIds:%v is nil", playerId, itemIds)
	}

	// 查询玩家道具信息
	itemList, err := rpcAsset.GetPlayerItemListInfo(ctx, playerId, interceptor.GetRPCOptions(ctx).ProductId, itemIds, commonPB.STORAGE_TYPE_ST_STORE)
	if err != nil {
		entry.Errorf("PlayerQueryItemInfoByIdList: playerId:%d, itemIds:%v, err:%+v", playerId, itemIds, err)
		return nil, err
	}

	return itemList, nil
}

// PlayerQueryItemInfo 查询玩家指定道具类型道具信息
func PlayerQueryItemInfo(ctx context.Context, playerId uint64, itemType commonPB.ITEM_TYPE) ([]*commonPB.ItemInfo, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 {
		return nil, fmt.Errorf("playerId is 0")
	}

	categoryList := make([]commonPB.ITEM_CATEGORY, 0)
	// 查询所有道具
	if int(itemType) == -1 {
		categoryList = item_kit.GetAllItemCategory()
	} else {
		categoryList = append(categoryList, item_kit.GetItemTypeCategory(itemType))
	}

	entry.Infof("query player:%d, itemType:%d, categoryList:%v", playerId, itemType, categoryList)

	itemList, err := rpcAsset.GetPlayerCategoryListItemInfo(ctx, playerId, interceptor.GetRPCOptions(ctx).ProductId, categoryList, commonPB.STORAGE_TYPE_ST_STORE)
	if err != nil {
		entry.Errorf("query player:%d, itemType:%d, categoryList:%v, err:%+v", playerId, itemType, categoryList, err)
		return nil, err
	}

	return itemList, nil
}

// OperatePlayerItem 处理玩家道具 (批量处理 +- 等)
//
// ctx：上下文对象，携带玩家基础信息 productId等
// playerId：玩家ID，uint64类型
// itemList：道具列表， []*commonPB.ItemBase
// optType：物品操作类型，commonPB.ITEM_OPERATION枚举类型
// srcType：物品来源类型，commonPB.ITEM_SOURCE_TYPE枚举类型
// storeType：物品存储类型，commonPB.STORAGE_TYPE枚举类型
// isUnpack：是否拆包，bool类型
//
// 返回值：
// commonPB.Reward: 奖励信息，如果操作成功则返回nil，否则返回非nil的错误信息
// error：如果操作成功则返回nil，否则返回非nil的错误信息

func OperatePlayerItem(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase, optType commonPB.ITEM_OPERATION, srcType commonPB.ITEM_SOURCE_TYPE, storeType commonPB.STORAGE_TYPE, isUnpack bool) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)

	// 执行预检查
	err := preCheck(ctx, playerId, itemList, optType, storeType)
	if err != nil {
		entry.Errorf("preCheck failed: player=%d, optType=%v, err=%v", playerId, optType, err)
		return nil, err
	}

	// 产品id
	productId := interceptor.GetRPCOptions(ctx).ProductId

	if storeType <= commonPB.STORAGE_TYPE_ST_UNKNOWN {
		storeType = commonPB.STORAGE_TYPE_ST_STORE
	}

	// TODO 后续删除
	// if productId <= 0 {
	// 	productId = 1
	// }

	// 先决检查
	if playerId == 0 || len(itemList) <= 0 || productId <= 0 || optType == commonPB.ITEM_OPERATION_IO_UNKNOWN {
		entry.Errorf("operator player:%d, product:%d, item list:%d, optType:%d, some param is error", playerId, productId, len(itemList), optType)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "operator item playerId or itemList is nil")
	}

	// 转换结构 + 礼包拆包
	itemParamList := make([]*modelItem.ItemOptParam, 0)
	for _, item := range itemList {
		if item == nil || item.GetItemId() <= 0 || item.GetItemCount() <= 0 {
			entry.Errorf("operator player:%d item:%s is error", playerId, item.String())
			return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "operator item is error")
		}

		itemParam, err := modelItem.NewItemOptParam(ctx, item.GetItemId(), item.GetItemCount(), isUnpack)
		if err != nil || len(itemParam) <= 0 {
			entry.Errorf("operator player:%d, item:%s, to param error:%v", playerId, item.String(), err)
			return nil, fmt.Errorf("operator item itemParam is nil")
		}

		itemParamList = append(itemParamList, itemParam...)
	}

	// 组合道具信息
	newItemList := modelItem.MakeUpItem(itemParamList)

	// 数据预处理 添加道具属性
	if optType == commonPB.ITEM_OPERATION_IO_ADD {
		newItemList, err = item_dispose.DoItemList(ctx, newItemList)
		if err != nil {
			return nil, err
		}
	}

	// 筛选出钓场道具
	pondItemMap := make(map[int32][]*modelItem.ItemOptParam, 0)

	for _, itemParam := range newItemList {
		if itemParam == nil {
			continue
		}
		if item_kit.IsPondItemType(commonPB.ITEM_TYPE(itemParam.ItemType)) {
			_, ok := pondItemMap[itemParam.ItemType]
			if ok {
				pondItemMap[itemParam.ItemType] = append(pondItemMap[itemParam.ItemType], itemParam)
			} else {
				pondItemMap[itemParam.ItemType] = []*modelItem.ItemOptParam{itemParam}
			}
		}
	}

	lootPbList := make([]*commonPB.OriginLoot, 0, len(newItemList))
	for _, itemInfo := range newItemList {
		if itemInfo == nil {
			entry.Errorf("player:%d operate item failed: item info is nil, item list:%v", playerId, newItemList)
			return nil, fmt.Errorf("operate item failed: item info is nil")
		}

		lootProto, err := itemInfo.ToOriginLootProto()
		if err != nil {
			entry.Errorf("player:%d operate item to proto failed: %v", playerId, err)
			return nil, fmt.Errorf("item to proto failed")
		}

		lootPbList = append(lootPbList, lootProto)
	}

	// 组合成协议请求修改玩家道具
	rewardInfo, err := rpcAsset.PlayerOperateItemReq(ctx, playerId, productId, lootPbList, optType, srcType, storeType)
	if err != nil || rewardInfo == nil {
		entry.Errorf("operator playerId:%d, productId:%d, itemList len:%d, optType:%d, srcType:%d, rewardInfo:%v, err:%v", playerId, productId, len(newItemList), optType, srcType, rewardInfo, err)
		return nil, err
	}

	// 后处理
	err = postProcess(ctx, playerId, itemList, optType, storeType)
	if err != nil {
		entry.Warnf("post process failed: %v", err)
	}

	// 记录物品操作流水和数据分析
	recordItemOperation(ctx, playerId, itemList, rewardInfo, optType, srcType)

	// 通知客户端更新道具信息
	logicNotify.PlayerRewardInfoNotify(ctx, playerId, storeType, rewardInfo)

	// 异步化
	{
		// 处理玩家钓场道具(体力值)
		PlayerPondItemChange(ctx, playerId, optType, pondItemMap, rewardInfo)

		// 玩家经验等级变化处理
		lvRwMap, checkErr := logicRole.CheckPlayerExpLevelChange(ctx, playerId, rewardInfo)

		// 玩家升级 发送升级奖励
		if len(lvRwMap) > 0 && checkErr == nil {
			// 升级奖励
			SendPlayerLevelUpReward(ctx, playerId, lvRwMap)
		}

	}

	entry.Debugf("operator playerId:%d, productId:%d, itemList:%s, is_unpack:%v, optType:%d, srcType:%d, rewardInfo:%s", playerId, productId, modelItem.ItemBase2JsonStr(itemList), isUnpack, optType, srcType, rewardInfo.String())

	publish.PublishItemChange(ctx, playerId, optType, newItemList, srcType)

	return rewardInfo, nil
}

// OperateItemByInstance 操作玩家道具
func OperateItemByInstance(ctx context.Context, playerId uint64, itemList []*commonPB.OriginLoot, optType commonPB.ITEM_OPERATION, bagType commonPB.STORAGE_TYPE, srcType commonPB.ITEM_SOURCE_TYPE) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)
	if len(itemList) == 0 {
		return nil, fmt.Errorf("change is nil")
	}
	productId := 1

	rewardInfo, err := rpcAsset.PlayerOperateItemReq(ctx, playerId, 1, itemList, optType, srcType, bagType)
	if err != nil || rewardInfo == nil {
		entry.Errorf("operator playerId:%d, productId:%d, itemList len:%d, optType:%d, srcType:%d, rewardInfo:%v, err:%v", playerId, productId, len(itemList), optType, srcType, rewardInfo, err)
		return nil, err
	}
	// TODO 补充流水

	// TODO: 统一推送
	// 通知客户端更新道具信息
	logicNotify.PlayerRewardInfoNotify(ctx, playerId, bagType, rewardInfo)

	return rewardInfo, nil
}

// GetPlayerItemCd 获取玩家道具cd列表
func GetPlayerItemCd(ctx context.Context, playerId uint64) ([]*commonPB.ItemCdInfo, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 {
		return nil, fmt.Errorf("playerId:%d  is nil", playerId)
	}

	// 查询玩家道具冷却信息
	cdMap, err := daoItemCd.GetPlayerItemCdRds(ctx, playerId)
	if err != nil {
		entry.Errorf("get player:%d item cd error:%v", playerId, err)
		return nil, err
	}

	// 转成协议
	cdList := make([]*commonPB.ItemCdInfo, 0, len(cdMap))
	for _, v := range cdMap {
		cdList = append(cdList, v.ToProto())
	}

	return cdList, nil

}
