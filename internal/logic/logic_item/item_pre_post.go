package logicItem

import (
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	daoItemCd "hallsrv/internal/dao/dao_item_cd"
	modelItem "hallsrv/internal/model/model_item"
)

type ItemProcessor interface {
	// PreCheck 预检查
	PreCheck(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase, storeType commonPB.STORAGE_TYPE) error
	// PostProcess 后置处理
	PostProcess(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase, storeType commonPB.STORAGE_TYPE) error
}

// GetItemProcessor 根据操作类型获取对应的预检查处理器
func getItemProcessor(optType commonPB.ITEM_OPERATION) ItemProcessor {
	switch optType {
	case commonPB.ITEM_OPERATION_IO_REDUCE:
		return &reduceItem{}
	default:
		return nil
	}
}

// preCheck 道具操作预检查函数
func preCheck(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase, optType commonPB.ITEM_OPERATION, storeType commonPB.STORAGE_TYPE) error {
	// 获取对应的预检查处理器
	processor := getItemProcessor(optType)
	if processor == nil {
		return nil
	}

	return processor.PreCheck(ctx, playerId, itemList, storeType)
}

// postProcess 道具操作后置处理函数
func postProcess(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase, optType commonPB.ITEM_OPERATION, storeType commonPB.STORAGE_TYPE) error {
	// 获取对应的后置处理处理器
	processor := getItemProcessor(optType)
	if processor == nil {
		return nil
	}

	return processor.PostProcess(ctx, playerId, itemList, storeType)
}

// reduceItem 减少道具
type reduceItem struct{}

func (r *reduceItem) PreCheck(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase, storeType commonPB.STORAGE_TYPE) error {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("reduce preCheck: player=%d, itemCount=%d", playerId, len(itemList))

	// 获取冷却配置
	itemCdConf := cmodel.GetAllItemCd()
	if itemCdConf == nil {
		return nil
	}

	// 筛选需要检查冷却的道具
	checkItemIds := make([]int64, 0, len(itemList))
	for _, item := range itemList {
		if item == nil || item.GetItemId() <= 0 || item.GetItemCount() <= 0 {
			return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, fmt.Sprintf("invalid item: %v", item))
		}
		ex, ok := itemCdConf[item.GetItemId()]
		if !ok || ex == nil || ex.CdSecond <= 0 {
			continue
		}
		checkItemIds = append(checkItemIds, item.GetItemId())
	}
	if len(checkItemIds) == 0 {
		return nil
	}

	// 查询用户道具冷却缓存
	playerItemCdMap, err := daoItemCd.GetPlayerItemCdRds(ctx, playerId)
	if err != nil {
		return err
	}

	for _, itemId := range checkItemIds {
		cdInfo, ok := playerItemCdMap[itemId]
		if !ok || cdInfo == nil {
			continue
		}
		if cdInfo.LastUseTime+itemCdConf[itemId].CdSecond > timex.Now().Unix() {
			return protox.CodeError(commonPB.ErrCode_ERR_HALL_ITEM_CD, "The props are cooling down.")
		}

	}

	entry.Debugf("reduce preCheck passed: player=%d, all items sufficient", playerId)
	return nil
}

func (r *reduceItem) PostProcess(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase, storeType commonPB.STORAGE_TYPE) error {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("reduce post process: player=%d, itemList=%v", playerId, itemList)

	// 获取冷却配置
	itemCdConf := cmodel.GetAllItemCd()
	if itemCdConf == nil {
		return nil
	}

	// 判断道具是否有冷却
	cdItemIds := make([]*modelItem.TItemCd, 0, len(itemList))
	for _, item := range itemList {
		if item == nil || item.ItemId <= 0 || item.GetItemCount() <= 0 {
			continue
		}
		ex, ok := itemCdConf[item.ItemId]
		if !ok || ex == nil || ex.CdSecond <= 0 {
			continue
		}
		cdItemIds = append(cdItemIds, &modelItem.TItemCd{
			PlayerId:    playerId,
			ItemId:      item.ItemId,
			LastUseTime: timex.Now().Unix(),
		})
	}
	if len(cdItemIds) == 0 {
		return nil
	}

	// 设置冷却
	err := daoItemCd.UpdatePlayerItemCdRds(ctx, playerId, cdItemIds)
	if err != nil {
		return err
	}

	return nil
}
