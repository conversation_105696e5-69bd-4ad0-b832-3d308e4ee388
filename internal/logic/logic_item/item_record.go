package logicItem

import (
	"context"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_hall"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	modelItem "hallsrv/internal/model/model_item"
	"hallsrv/internal/repo/record"
)

// recordItemOperation 记录物品操作流水和数据分析
func recordItemOperation(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase, rewardInfo *commonPB.Reward, optType commonPB.ITEM_OPERATION, srcType commonPB.ITEM_SOURCE_TYPE) {
	// clickhouse
	rData := &record.ItemRecordParam{
		Ctx:          ctx,
		OptType:      int32(optType),
		OriginalData: modelItem.ItemBase2JsonStr(itemList),
		PlayerId:     playerId,
		RewardInfo:   rewardInfo,
	}
	record.DefaultLogging.PubItemRecord(rData)

	// 数数
	teRecord := a_hall.TeItemInfo{
		DefaultHeader: analyze.NewTeDefaultHeaderFromCtx(ctx),
		TeItem: &a_hall.TeItem{
			Items:       rewardInfo.ItemList,
			Source:      srcType,
			ChangeType:  optType,
			OperateTime: timex.Now(),
		},
	}
	record.LogItemRecord(ctx, &teRecord)
}
