package item_dispose

import (
	"context"
	modelItem "hallsrv/internal/model/model_item"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

type IItemDispose interface {
	Do(ctx context.Context, item *modelItem.ItemOptParam) ([]*modelItem.ItemOptParam, error)
}

// DoItemList重新处理列表
func DoItemList(ctx context.Context, list []*modelItem.ItemOptParam) ([]*modelItem.ItemOptParam, error) {
	newList := make([]*modelItem.ItemOptParam, 0)
	for _, item := range list {
		dispose := GetDispose(commonPB.ITEM_TYPE(item.ItemType))
		rtnList, err := dispose.Do(ctx, item)
		if err != nil {
			return nil, err
		}
		newList = append(newList, rtnList...)
	}
	return newList, nil
}

func GetDispose(itemType commonPB.ITEM_TYPE) IItemDispose {
	switch itemType {
	case commonPB.ITEM_TYPE_IT_TACKLE_RODS, commonPB.ITEM_TYPE_IT_TACKLE_REEl, commonPB.ITEM_TYPE_IT_TACKLE_LINE, commonPB.ITEM_TYPE_IT_TACKLE_LEADER, commonPB.ITEM_TYPE_IT_TACKLE_BOBBERS:
		return &equipDispose{}
	case commonPB.ITEM_TYPE_IT_TACKLE_LURES, commonPB.ITEM_TYPE_IT_TACKLE_BAIT, commonPB.ITEM_TYPE_IT_TACKLE_HOOKS:
		return &baitDispose{}
	default:
		return &defaultDispose{}
	}
}

type defaultDispose struct {
}

// 原样返回
func (d *defaultDispose) Do(ctx context.Context, item *modelItem.ItemOptParam) ([]*modelItem.ItemOptParam, error) {
	list := []*modelItem.ItemOptParam{item}
	return list, nil
}
