package item_dispose

import (
	"context"
	modelItem "hallsrv/internal/model/model_item"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
)

// 装备处理模式
// 装备：
// 定义范围： 杆 线 轮 前导
// 需要拥有独立instanceId
type equipDispose struct {
}

func (d *equipDispose) Do(ctx context.Context, item *modelItem.ItemOptParam) ([]*modelItem.ItemOptParam, error) {
	newList := make([]*modelItem.ItemOptParam, 0)
	cfg, err := item_kit.GetTackleConf(ctx, item.ItemId)
	if err != nil {
		return nil, err
	}
	// for i := 0; i < int(item.ItemNum); i++ {
	new := item.Copy()
	new.InstanceId = random.GetUUID()
	// 装备类型堆叠为1
	new.ItemNum = item.ItemNum
	// 初始化额外属性
	var dilatation_coefficient int32 = 10000 // 耐久扩大系数

	// TODO 有可能溢出 可能要线转换为int64
	durability := int64(cfg.Durability) * (int64(dilatation_coefficient) + int64(cfg.DurabilityCoeff)) / int64(dilatation_coefficient)
	new.Extra[int32(commonPB.ITEM_EXTRA_KEY_IEK_CURR_DURABILITY)] = (durability)
	new.Extra[int32(commonPB.ITEM_EXTRA_KEY_IEK_MAX_DURABILITY)] = (durability)

	newList = append(newList, new)
	// }

	return newList, nil
}
