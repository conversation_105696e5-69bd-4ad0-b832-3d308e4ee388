package item_dispose

import (
	"context"
	modelItem "hallsrv/internal/model/model_item"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

type baitDispose struct {
}

func (d *baitDispose) Do(ctx context.Context, item *modelItem.ItemOptParam) ([]*modelItem.ItemOptParam, error) {
	newList := make([]*modelItem.ItemOptParam, 0)
	new := item.Copy()
	// 固定生成10000点耐久度 等价原本万分比耐久度
	var durailbity int64 = 10000
	new.Extra[int32(commonPB.ITEM_EXTRA_KEY_IEK_CURR_DURABILITY)] = durailbity
	new.Extra[int32(commonPB.ITEM_EXTRA_KEY_IEK_MAX_DURABILITY)] = durailbity

	newList = append(newList, new)

	return newList, nil

}
