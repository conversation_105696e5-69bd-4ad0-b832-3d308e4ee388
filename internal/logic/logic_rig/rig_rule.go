package logicRig

import (
	"fmt"
	"strconv"
	"strings"
	"sync"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"github.com/sirupsen/logrus"
)

var (
	ruleCache sync.Map
)

// LoadRigRuleCache 加载钓组规则缓存 TODO: 后续提供给gm调用
func LoadRigRuleCache() error {
	// 使用配置加载
	rigRuleConf := cmodel.GetAllRigRule()
	if rigRuleConf == nil {
		logrus.Fatalf("get rig rule config fail")
		return fmt.Errorf("get rig rule config fail")
	}

	// 重置数据
	for ruleId, rigRule := range rigRuleConf {
		ruleStrArr := RigRuleToStr(rigRule)
		if len(ruleStrArr) != 0 {
			for _, ruleStr := range ruleStrArr {
				ruleCache.Store(ruleStr, ruleId)
			}
		} else {
			logrus.Errorf("rule %d config error", ruleId)
		}
	}

	logrus.Infof("rig rule cache load success")

	return nil
}

// RigRuleToStr 规则数据转化string 以"|"分割
func RigRuleToStr(rigRule *cmodel.RigRule) []string {
	if rigRule == nil {
		return []string{}
	}
	list := make([]string, 0)
	// 构建叉乘表
	// 赋值默认值
	rodTypes := rigRule.RodType
	if len(rodTypes) == 0 {
		rodTypes = append(rodTypes, 0)
	}
	reelTypes := rigRule.ReelType
	if len(reelTypes) == 0 {
		reelTypes = append(reelTypes, 0)
	}
	lineTypes := rigRule.LineType
	if len(lineTypes) == 0 {
		lineTypes = append(lineTypes, 0)
	}
	leaderTypes := rigRule.LeaderType
	if len(leaderTypes) == 0 {
		leaderTypes = append(leaderTypes, 0)
	}
	floatTypes := rigRule.FloatType
	if len(floatTypes) == 0 {
		floatTypes = append(floatTypes, 0)
	}
	baitTypes := rigRule.BaitType
	if len(baitTypes) == 0 {
		baitTypes = append(baitTypes, 0)
	}
	hookTypes := rigRule.HookType
	if len(hookTypes) == 0 {
		hookTypes = append(hookTypes, 0)
	}

	for _, rodType := range rodTypes {
		for _, reelType := range reelTypes {
			for _, lineType := range lineTypes {
				for _, leaderType := range leaderTypes {
					for _, floatType := range floatTypes {
						for _, baitType := range baitTypes {
							for _, hookType := range hookTypes {
								str := makeRuleStr(rodType, reelType, lineType, leaderType, floatType, baitType, hookType)
								list = append(list, str)
							}
						}
					}
				}
			}
		}
	}

	return list
}

func makeRuleStr(rodType int32, reelType int32, lineType int32, leaderType int32, floatType int32, baitType int32, hookType int32) string {
	return fmt.Sprintf("%d|%d|%d|%d|%d|%d|%d", rodType, reelType, lineType, leaderType, floatType, baitType, hookType)
}

// SubTypeArrToStr 钓组子类型列表转化为string
func SubTypeArrToStr(typeArr []int32) string {
	if typeArr == nil {
		return ""
	}

	var ruleStr string
	for _, typeId := range typeArr {
		ruleStr += fmt.Sprintf("%d|", typeId)
	}

	return ruleStr[:len(ruleStr)-1]
}

// GetRigRuleId 校验规则是否合法 返回配置规则id
func GetRigRuleId(typeArr []int32) int64 {
	if len(typeArr) <= 0 {
		return 0
	}

	ruleStr := SubTypeArrToStr(typeArr)

	var curRule int64

	ruleCache.Range(func(key any, value any) bool {
		srcRuleStr, ruleId := key.(string), value.(int64)
		if matchString(srcRuleStr, ruleStr) {
			curRule = ruleId
			return false
		}
		return true
	})

	return curRule
}

// matchString 检查是否匹配 使用前缀校验 "1|2|3|2|1" 和 "1|2"
func matchString(src, dst string) bool {
	if len(src) == 0 || len(dst) == 0 {
		return false
	}

	// 检查 dst 是否是 src 的前缀
	return strings.HasPrefix(src, dst)
}

// GetPrefixMatchTypeList 获取前缀匹配的类型列表(客户端使用)
func GetPrefixMatchTypeList(typeArr []int32) [][]int32 {
	if len(typeArr) <= 0 {
		return nil
	}

	ruleStr := SubTypeArrToStr(typeArr)

	matchTypeArr := make([][]int32, 0)

	ruleCache.Range(func(key any, value any) bool {
		srcRuleStr, _ := key.(string), value.(int64)
		if matchString(srcRuleStr, ruleStr) {
			matchTypeArr = append(matchTypeArr, parseStringToInt32List(srcRuleStr, "|"))
		}
		return true
	})

	return matchTypeArr
}

func parseStringToInt32List(input string, splitStr string) []int32 {
	// 解析出字符串中的数字
	parts := strings.Split(input, splitStr)

	// 转换成int32
	var intList []int32
	for _, part := range parts {
		trimmedPart := strings.TrimSpace(part)
		intValue, err := strconv.Atoi(trimmedPart)
		if err != nil {
			intValue = 0
		}
		intList = append(intList, int32(intValue))
	}

	return intList
}
