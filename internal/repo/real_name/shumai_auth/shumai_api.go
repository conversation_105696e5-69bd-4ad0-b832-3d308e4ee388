package shumai_auth

import (
	"context"
	"encoding/json"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"hallsrv/internal/config"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/httpx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

const urlShuMaiSdk = "https://eid.shumaidata.com/eid/check"                            // 实名认证
const urlShuMaiHolidaySdk = "https://jrxxcxsmkj.market.alicloudapi.com/holiday/search" // 节假日

type ShuMaiRet struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Success bool        `json:"success"`
	Data    interface{} `json:"data"`
	Result  struct {
		Name        string `json:"name"`
		Mobile      string `json:"mobile"`
		IdCard      string `json:"idcard"`
		Res         string `json:"res"`
		Description string `json:"description"`
		Sex         string `json:"sex"`
		Birthday    string `json:"birthday"`
		Address     string `json:"address"`
	} `json:"result"`
}

type ShuMaiHolidayRet struct {
	Msg     string `json:"msg"`
	Success bool   `json:"success"`
	Code    int    `json:"code"`
	Data    struct {
		OrderNo       string `json:"orderNo"`
		Holiday       string `json:"holiday"`
		RetCode       string `json:"ret_code"`
		WeekDay       int    `json:"weekDay"`
		Cn            string `json:"cn"`
		HolidayRemark string `json:"holiday_remark"`
		En            string `json:"en"`
		Day           string `json:"day"`
		Type          string `json:"type"`
		End           string `json:"end"`
		Begin         string `json:"begin"`
	} `json:"data"`
}

// ShuMaiRealNameAuth 数脉SDK实名
func ShuMaiRealNameAuth(ctx context.Context, name, idNum, iphone string) (ShuMaiRet, error) {
	entry := logx.NewLogEntry(ctx)
	shuMaiAuthRet := ShuMaiRet{}

	// 数脉 Token配置获取
	appCode := ""
	authCfg := cmodel.GetRealNameAuth(consul_config.WithGrpcCtx(ctx))
	if authCfg != nil {
		appCode = authCfg.ShumaiAppCode
	}

	params := map[string]string{
		"idcard": idNum,
		"mobile": iphone,
		"name":   name,
	}

	// 设置请求头
	headers := make(map[string]string)
	headers["Content-Type"] = "application/x-www-form-urlencoded"
	headers["Authorization"] = "APPCODE " + appCode

	ret, err := httpx.PostBackFormByHeader(urlShuMaiSdk, headers, params)
	if err != nil {
		entry.Errorf("[ShuMai] RealNameAuth err : %v, ret:%s", err, ret)
		return shuMaiAuthRet, protox.PB2Error(commonPB.ErrCode_ERR_FAIL)
	}

	if err = json.Unmarshal(ret, &shuMaiAuthRet); err != nil {
		entry.Errorf("Json parse err : %v", err)
		return shuMaiAuthRet, protox.PB2Error(commonPB.ErrCode_ERR_JSON_PARSE_ERROR)
	}
	return shuMaiAuthRet, nil
}

// ShuMaiHoliday 查询节假日状态 传入年月日 如:20241001
func ShuMaiHoliday(ctx context.Context, day string) (*ShuMaiHolidayRet, error) {
	shuMaiHolidayRet := &ShuMaiHolidayRet{}
	entry := logx.NewLogEntry(ctx)

	// 数脉 Token配置获取
	appCode := ""
	cfg := cmodel.GetRealNameAuth(consul_config.WithGrpcCtx(ctx))
	if cfg != nil {
		appCode = cfg.ShumaiAppCode
	}

	headers := map[string]string{
		"Authorization": "APPCODE " + appCode,
	}

	queryArray := map[string]string{
		"day": day,
	}

	result, err := httpx.GetBackFormByHeader(urlShuMaiHolidaySdk, headers, queryArray)
	if err != nil {
		entry.Errorf("[ShuMai] Holiday info err : %v ", err)
		return shuMaiHolidayRet, protox.PB2Error(commonPB.ErrCode_ERR_FAIL)
	}

	if err = json.Unmarshal(result, shuMaiHolidayRet); err != nil {
		entry.Errorf("Json parse result:%+v err : %v", shuMaiHolidayRet, err)
		return shuMaiHolidayRet, protox.PB2Error(commonPB.ErrCode_ERR_JSON_PARSE_ERROR)
	}

	return shuMaiHolidayRet, nil
}

// QueryCurHolidayType 查询当天节假日类型
func QueryCurHolidayType(ctx context.Context) (string, error) {
	entry := logx.NewLogEntry(ctx)
	nowTime := timex.Now()
	// 转化成年月日
	curDate := nowTime.Format("20060102") // 20241015
	intDate := transform.Str2Int64(curDate)

	// 先查询redis
	holidayInfo, err := redisx.GetGameCli().Get(ctx, config.RDS_KEY_HOLIDAY_INFO).Result()

	shuMaiHoliday := &ShuMaiHolidayRet{}
	if err == nil && json.Unmarshal([]byte(holidayInfo), shuMaiHoliday) == nil {
		// 检查是否有该日期
		if curDate == shuMaiHoliday.Data.Day || (intDate >= transform.Str2Int64(shuMaiHoliday.Data.Begin) && intDate <= transform.Str2Int64(shuMaiHoliday.Data.End)) {
			entry.Debugf("holiday info found, data:%+v, date:%+v", shuMaiHoliday.Data, curDate)
			return shuMaiHoliday.Data.Type, nil
		}
	}

	entry.Infof("holiday info not found, query from shumai, date:%+v", curDate)

	// 查询数脉节假日信息
	holidayRet, err := ShuMaiHoliday(ctx, curDate)
	if err != nil {
		return "0", protox.PB2Error(commonPB.ErrCode_ERR_FAIL)
	}

	strHoliday, err := json.Marshal(holidayRet)
	if err != nil {
		entry.Errorf("Json marshal:%+v err:%v", strHoliday, err)
		return "0", protox.PB2Error(commonPB.ErrCode_ERR_JSON_PARSE_ERROR)
	}

	// 过期时间计算
	expireTime := time.Hour * 24
	if holidayRet.Data.Type != "1" && transform.Str2Int64(holidayRet.Data.End) > intDate {
		addDay := transform.Str2Int64(holidayRet.Data.End) - int64(intDate)
		expireTime = time.Hour * 24 * time.Duration(addDay)
	}

	// 缓存结果
	redisx.GetGameCli().Set(ctx, config.RDS_KEY_HOLIDAY_INFO, strHoliday, expireTime)

	entry.Infof("holiday info:%+v", holidayRet)

	return shuMaiHoliday.Data.Type, nil
}
