package office_auth

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	urlCheck    = "https://api.wlc.nppa.gov.cn/idcard/authentication/check"  // 中宣部API
	urlQuery    = "http://api2.wlc.nppa.gov.cn/idcard/authentication/query"  // 中宣部API
	urlLoginOut = "http://api2.wlc.nppa.gov.cn/behavior/collection/loginout" // 中宣部API
)

// Fcm 中宣部官方API
type Fcm struct {
	AppID  string
	BizID  string
	Key    string
	aes    cipher.AEAD
	keys   []string
	client http.Client
}

func NewFcmInstance(appId, bizId, key string) (*Fcm, error) {
	fcm := &Fcm{
		AppID: appId,
		BizID: bizId,
		Key:   key,
		keys:  []string{"appId", "bizId", "timestamps"},
		client: http.Client{
			Timeout: 10 * time.Second,
		},
	}

	// cipher
	b, err := hex.DecodeString(key)
	if err != nil {
		return nil, err
	}
	block, err := aes.NewCipher(b)
	if err != nil {
		return nil, err
	}

	// ahead
	ahead, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}
	fcm.aes = ahead
	return fcm, nil
}

func (f *Fcm) Check(c *Check) (*http.Response, error) {
	header := f.getHeader()
	header["Content-Type"] = []string{"application/json; charset=utf-8"}
	return f.request("POST", urlCheck, c, header, nil)
}

func (f *Fcm) Query(q *Query) (*http.Response, error) {
	header := f.getHeader()
	var data map[string]string
	if value, err := json.Marshal(q); err == nil {
		if err := json.Unmarshal(value, &data); err != nil {
			return nil, err
		}
	} else {
		return nil, err
	}
	uri := fmt.Sprintf("%s?%s=%s", urlQuery, "ai", q.Ai)
	return f.request("GET", uri, nil, header, data)
}

func (f *Fcm) LoginOrOut(c *Collections) (*http.Response, error) {
	header := f.getHeader()
	header["Content-Type"] = []string{"application/json; charset=utf-8"}
	return f.request("POST", urlLoginOut, c, header, nil)
}

// SetClient 请求
func (f *Fcm) SetClient(transport http.RoundTripper, timeout time.Duration) {
	f.client = http.Client{
		Transport: transport,
		Timeout:   timeout,
	}
}

// aes-128-gcm + base64
func (f *Fcm) makeBody(body []byte) (string, error) {
	// random bytes
	nonce := make([]byte, f.aes.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}
	data := append(nonce, f.aes.Seal(nil, nonce, body, nil)...)
	return base64.StdEncoding.EncodeToString(data), nil
}

// sha256
func (f *Fcm) makeSign(header http.Header, body string, query map[string]string) string {
	// except content-type
	header.Del("Content-Type")
	ks := f.keys
	for k, v := range query {
		ks = append(ks, k)
		header[k] = []string{v} // maybe lower case
	}
	sort.Strings(ks)
	raw := ""
	for _, k := range ks {
		raw += k + header[k][0]
	}
	hash := sha256.New()
	d := append(append([]byte(f.Key), raw...), body...)
	hash.Write(d)
	return hex.EncodeToString(hash.Sum(nil))
}

// get the header
func (f *Fcm) getHeader() http.Header {
	return http.Header{
		"appId":      []string{f.AppID},
		"bizId":      []string{f.BizID},
		"timestamps": []string{strconv.FormatInt(timex.Now().Unix()*1000, 10)},
	}
}

// request
func (f *Fcm) request(method, uri string, b interface{}, header http.Header, query map[string]string) (*http.Response, error) {
	var body []byte
	var err error
	if b != nil {
		body, err = json.Marshal(b)
		if err != nil {
			return nil, err
		}
	} else {
		body = []byte(`{}`)
	}

	data, err := f.makeBody(body)
	if err != nil {
		return nil, err
	}
	raw := `{"data":"` + strings.TrimRight(data, "=") + `"}`
	header["sign"] = []string{f.makeSign(header.Clone(), raw, query)}
	req, err := http.NewRequest(method, uri, bytes.NewReader([]byte(raw)))
	if err != nil {
		return nil, err
	}
	req.Header = header
	return f.client.Do(req)
}
