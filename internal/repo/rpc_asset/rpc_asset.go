package rpcAsset

import (
	"context"
	"fmt"
	assetRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/assetrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	modelItem "hallsrv/internal/model/model_item"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_asset"
)

// GetPlayerItemListInfo 查询玩家道具信息
func GetPlayerItemListInfo(ctx context.Context, playerId uint64, productId int32, itemIdList []int64, storageType commonPB.STORAGE_TYPE) ([]*commonPB.ItemInfo, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || len(itemIdList) <= 0 {
		entry.Errorf("get player item info failed: invalid param, playerId:%d, itemId:%v", playerId, itemIdList)
		return nil, fmt.Errorf("get player item info failed: invalid param")
	}

	assetRpcCli := crpc_asset.GetAssetRpcInstance().GetAssetRpcClient()

	if assetRpcCli == nil {
		return nil, fmt.Errorf("get asset rpc client failed")
	}

	itemModelList := make([]*modelItem.ItemOptParam, 0)
	for _, itemId := range itemIdList {
		newModels, err := modelItem.NewItemOptParam(ctx, itemId, 0, false)
		if err != nil {
			entry.Errorf("get player item info failed: %v", err)
			return nil, fmt.Errorf("get player item info failed")
		}

		itemModelList = append(itemModelList, newModels...)
	}

	reqItemList := make([]*commonPB.Item, 0, len(itemModelList))
	for _, itemModel := range itemModelList {
		reqItem := itemModel.ToItemProto()
		if reqItem == nil {
			entry.Errorf("get player item info failed: %v", itemModel)
			return nil, fmt.Errorf("get player item info failed")
		}
		reqItemList = append(reqItemList, reqItem)
	}

	req := &assetRpc.QueryItemNumReq{
		ProductId: 1,
		PlayerId:  playerId,
		Items:     reqItemList,
		Storage:   storageType,
	}

	rsp, err := assetRpcCli.QueryItemNum(ctx, req)
	if err != nil || rsp == nil {
		entry.Errorf("get player item list:%v info failed: %v, rsp:%v", itemIdList, err, rsp)
		return nil, fmt.Errorf("get player category list info failed")
	}

	if rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		entry.Errorf("get player category list info failed: %v", rsp)
		return nil, fmt.Errorf("get player category list info failed")
	}

	entry.Infof("get player item list:%v info success, rsp:%v", itemIdList, rsp)

	return rsp.ItemInfo, nil
}

// GetPlayerCategoryListItemInfo 查询玩家category列表的道具信息
func GetPlayerCategoryListItemInfo(ctx context.Context, playerId uint64, productId int32, categoryList []commonPB.ITEM_CATEGORY, bagType commonPB.STORAGE_TYPE) ([]*commonPB.ItemInfo, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || len(categoryList) <= 0 {
		entry.Errorf("get player category list info failed: invalid param, playerId:%d, productId:%d, categoryList:%v", playerId, productId, categoryList)
		return nil, fmt.Errorf("get player category list info failed: invalid param")
	}

	// 去资产服查询
	assetRpcCli := crpc_asset.GetAssetRpcInstance().GetAssetRpcClient()

	if assetRpcCli == nil {
		return nil, fmt.Errorf("get asset rpc client failed")
	}

	req := &assetRpc.QueryCategoryReq{
		PlayerId:  playerId,
		ProductId: productId,
		Category:  categoryList,
		Storage:   bagType,
	}

	rsp, err := assetRpcCli.QueryCategory(ctx, req)
	if err != nil || rsp == nil {
		entry.Errorf("get player category list info failed: %v, rsp:%v", err, rsp)
		return nil, fmt.Errorf("get player category list info failed")
	}

	if rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		entry.Errorf("get player category list info failed: %v", rsp)
		return nil, fmt.Errorf("get player category list info failed")
	}

	entry.Infof("get player category list:%v info success, rsp:%v", categoryList, rsp)

	return rsp.ItemList, nil
}

// PlayerOperateItemReq 操作道具请求
func PlayerOperateItemReq(ctx context.Context, playerId uint64, productId int32, itemList []*commonPB.OriginLoot, optType commonPB.ITEM_OPERATION, srcType commonPB.ITEM_SOURCE_TYPE, bagType commonPB.STORAGE_TYPE) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)
	if len(itemList) == 0 {
		return nil, fmt.Errorf("operate item failed: item list is empty")
	}

	assetRpcCli := crpc_asset.GetAssetRpcInstance().GetAssetRpcClient()

	if assetRpcCli == nil {
		return nil, fmt.Errorf("get asset rpc client failed")
	}

	// 操作道具唯一凭证
	claimId := random.GenerateTransactionID(playerId)

	rpcReq := &assetRpc.OperateItemReq{
		ProductId:     productId,
		PlayerId:      playerId,
		ItemOperation: optType,
		Source:        srcType,
		Loots:         itemList,
		ClaimID:       claimId,
		Storage:       bagType,
	}

	// 调用资产服
	rpcRsp, err := assetRpcCli.OperateItem(ctx, rpcReq)
	if err != nil {
		entry.Errorf("player:%d operate item:%v, req:%v, failed:%v", playerId, itemList, rpcReq, err)
		return nil, err
	}

	if rpcRsp.GetRet().GetCode() != commonPB.ErrCode_ERR_SUCCESS {
		entry.Errorf("player:%d operate item failed: %v", playerId, rpcRsp)
		return nil, fmt.Errorf("%v", rpcRsp.Ret)
	}

	// 添加关键日志
	entry.Infof("player:%d operate item:%v, optType:%d, srcType:%d, success, rsp:%s", playerId, itemList, optType, srcType, rpcRsp.String())

	rewardInfo := &commonPB.Reward{
		ClaimID:    claimId,
		SourceType: srcType,
		Timestamp:  timex.Now().Unix(),
		ItemList:   rpcRsp.GetItemList(),
	}

	return rewardInfo, nil
}

// PlayerMoveItemReq 移动物品
func PlayerMoveItemReq(ctx context.Context, playerId uint64, from, to commonPB.STORAGE_TYPE, itemList []*modelItem.ItemOptParam) (fromChange, toChange []*commonPB.ItemInfo, err error) {
	if len(itemList) == 0 {
		return nil, nil, fmt.Errorf("operate item failed: item list is empty")
	}
	entry := logx.NewLogEntry(ctx)
	productId := 1

	assetRpcCli := crpc_asset.GetAssetRpcInstance().GetAssetRpcClient()
	if assetRpcCli == nil {
		return nil, nil, fmt.Errorf("get asset rpc client failed")
	}
	lootPbList := make([]*commonPB.OriginLoot, 0, len(itemList))
	for _, itemInfo := range itemList {
		loot, err := itemInfo.ToOriginLootProto()
		if err != nil {
			return nil, nil, err
		}
		lootPbList = append(lootPbList, loot)
	}
	rpcReq := &assetRpc.MoveItemReq{
		ProductId: int32(productId),
		PlayerId:  playerId,
		From:      from,
		To:        to,
		ItemList:  lootPbList,
	}

	rpcRsp, err := assetRpcCli.MoveItem(ctx, rpcReq)
	if err != nil {
		entry.Errorf("move item failed:%+v req:%+v", err, rpcReq)
		return nil, nil, err
	}
	if rpcRsp.GetRet().GetCode() != commonPB.ErrCode_ERR_SUCCESS {
		return nil, nil, fmt.Errorf("%v", rpcRsp.GetRet())
	}
	entry.Debugf("moveItem successful req:%+v rsp:%+v", rpcReq, rpcRsp)

	return rpcRsp.FromChange, rpcRsp.ToChange, nil
}
