package services

import (
	"context"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	logic "hallsrv/internal/logic/logic_red_dot"
	modelReddot "hallsrv/internal/model/model_red_dot"
	"sync"
	"time"

	"hallsrv/internal/logic/logic_bag"
	"hallsrv/internal/logic/logic_durable"
	logicItem "hallsrv/internal/logic/logic_item"
	logicRig "hallsrv/internal/logic/logic_rig"
	"hallsrv/internal/repo/real_name/shumai_auth"

	logicPlayer "hallsrv/internal/logic/logic_player"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hallrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

type HallService struct {
}

var (
	onceHall = &sync.Once{}
	instance *HallService
)

func GetHallServiceInstance() *HallService {
	if instance != nil {
		return instance
	}

	onceHall.Do(func() {
		instance = &HallService{}
	})
	return instance
}

// OptPlayerItem 操作玩家道具 (+-)
func (s *HallService) OptPlayerItem(ctx context.Context, req *hallRpc.OptPlayerItemReq) (*hallRpc.OptPlayerItemRsp, error) {
	rsp := &hallRpc.OptPlayerItemRsp{Ret: protox.DefaultResult()}
	entry := logx.NewLogEntry(ctx)

	// 校验参数
	if req.GetPlayerId() == 0 || len(req.GetItemList()) == 0 ||
		req.GetItemOperation() == commonPB.ITEM_OPERATION_IO_UNKNOWN ||
		req.GetItemSource() == commonPB.ITEM_SOURCE_TYPE_IST_UNKNOWN {
		entry.Errorf("opt player item error, param is invalid, req:%s", req.String())
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM, "error param")
		return rsp, nil
	}

	// 添加奖励
	rewordInfo, err := logicItem.OperatePlayerItem(ctx, req.GetPlayerId(), req.GetItemList(), req.GetItemOperation(), req.GetItemSource(), commonPB.STORAGE_TYPE_ST_STORE, req.GetIsUnpack())
	if err != nil || rewordInfo == nil {
		entry.Errorf("opt item error req:%s, err:%s", req.String(), err)
		return rsp, nil
	}

	// 返回结果
	rsp.RewardInfo = rewordInfo
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Infof("opt player item success, req:%s, rsp:%s", req.String(), rsp.String())

	return rsp, nil
}

// QueryPlayerBaseInfoRsp 查询玩家基础信息
func (s *HallService) QueryPlayerBaseInfo(ctx context.Context, req *hallRpc.QueryPlayerBaseInfoReq) (*hallRpc.QueryPlayerBaseInfoRsp, error) {
	rsp := &hallRpc.QueryPlayerBaseInfoRsp{}
	entry := logx.NewLogEntry(ctx)
	playerId := req.GetPlayerId()

	// 校验参数
	if playerId == 0 {
		entry.Errorf("query player base info error, param is invalid,req:%s", req.String())
		return rsp, nil
	}

	// 查询玩家信息
	playerInfo, err := logicPlayer.QueryPlayerInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("player:%d, query player info error: %v", playerId, err)
		return rsp, nil
	}

	rsp.PlayerInfo = playerInfo

	return rsp, nil
}

// GmLoadRigRule gm加载竿架规则
func (s *HallService) GmLoadRigRule(ctx context.Context, req *hallRpc.GmLoadRuleReq) (*hallRpc.GmLoadRuleRsp, error) {
	err := logicRig.LoadRigRuleCache()
	return &hallRpc.GmLoadRuleRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)}, err
}

// 查询用户背包钓组信息
func (s *HallService) QueryPlayerRodInfo(ctx context.Context, req *hallRpc.QueryPlayerRodInfoReq) (*hallRpc.QueryPlayerRodInfoRsp, error) {
	rsp := &hallRpc.QueryPlayerRodInfoRsp{
		Ret: protox.DefaultResult(),
	}
	entry := logx.NewLogEntry(ctx)
	playerId := req.GetPlayerId()

	// 校验参数
	if playerId == 0 {
		entry.Errorf("query player base info error, param is invalid,req:%s", req.String())
		return rsp, nil
	}

	// TODO 查询用户钓组信息
	group, err := logic_bag.GetAllRodBag(ctx, playerId)
	if err != nil {
		return nil, err
	}
	for _, v := range group {
		if v.Id == req.GetId() {
			rsp.RodInfo = v.ToProto(ctx)
		}
	}
	if rsp.RodInfo == nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_NOT_EXIST)
		return rsp, nil
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	return rsp, nil
}

// QueryHolidayType 查询节假日类型
func (s *HallService) QueryHolidayType(ctx context.Context, req *hallRpc.QueryHolidayTypeReq) (*hallRpc.QueryHolidayTypeRsp, error) {
	rsp := &hallRpc.QueryHolidayTypeRsp{
		Ret: protox.DefaultResult(),
	}
	entry := logx.NewLogEntry(ctx)
	holidayType, err := shumai_auth.QueryCurHolidayType(ctx)
	if err != nil {
		entry.Errorf("query holiday type field, type:%+v, err:%+v", holidayType, err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp, nil
	}

	enType := commonPB.HOLIDAY_TYPE(transform.Str2Int32(holidayType))

	// 判断当前时间是周五 修改为周末类型
	nowTime := timex.Now()
	if nowTime.Weekday() == time.Friday {
		enType = commonPB.HOLIDAY_TYPE_HT_WEEKEND
	}

	rsp.Type = enType
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("query holiday type success, rsp:%+v", rsp)

	return rsp, nil
}

// LossRodDurability 鱼竿耐久扣除
func (s *HallService) LossRodDurability(ctx context.Context, req *hallRpc.LossRodDurabilityReq) (*hallRpc.LossRodDurabilityRsp, error) {
	rsp := &hallRpc.LossRodDurabilityRsp{Ret: protox.DefaultResult()}
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("[LossRodDurability] req:%+v", req)

	err := logic_bag.LossRodDurability(ctx, req.PlayerId, req.GetRodId(), req.GetChange())
	if err != nil {
		entry.Errorf("[LossRodDurability] fail %+v", err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp, nil
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	entry.Debugf("[LossRodDurability] rsp:%+v", rsp)

	return rsp, nil
}

// LossItemHeap 鱼饵耐久扣除
func (s *HallService) LossItemHeap(ctx context.Context, req *hallRpc.LossItemHeapReq) (*hallRpc.LossItemHeapRsp, error) {
	rsp := &hallRpc.LossItemHeapRsp{Ret: protox.DefaultResult()}

	entry := logx.NewLogEntry(ctx)
	entry.Debugf("LossItemHeapReq req %+v", req)

	err := logic_durable.LossItemHeap(ctx, req.GetPlayerId(), req.GetItemId(), req.GetDurability(), req.GetSourceType())
	if err != nil {
		entry.Errorf("LossItemHeapReq error %+v", err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp, err
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("loss itemHeap rsp:%+v", rsp)

	return rsp, nil
}

// SetPlayerRedDot 设置玩家红点状态
func (s *HallService) SetPlayerRedDot(ctx context.Context, req *hallRpc.SetPlayerRedDotReq) (*hallRpc.SetPlayerRedDotRes, error) {
	rsp := &hallRpc.SetPlayerRedDotRes{Ret: protox.DefaultResult()}
	entry := logx.NewLogEntry(ctx)

	// 校验参数
	if req.GetPlayerId() == 0 || req.GetModuleType() == commonPB.USER_MODULE_TYPE_UMT_UNKNOWN {
		entry.Errorf("set player red dot error, param is invalid, req:%s", req.String())
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM, "error param")
		return rsp, nil
	}

	// 获取红点服务
	var subList []modelReddot.SubModuleType
	for _, subID := range req.SubModuleType {
		subList = append(subList, modelReddot.SubModuleType(subID))
	}
	err := logic.SetRedDot(ctx, req.GetPlayerId(), req.GetModuleType(), subList)
	if err != nil {
		entry.Errorf("set player red dot failed: playerID=%d, moduleType=%d, subModuleType=%d, err=%v",
			req.GetPlayerId(), req.GetModuleType(), req.GetSubModuleType(), err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp, nil
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	entry.Infof("set player red dot success: playerID=%d, moduleType=%d, subModuleType=%d",
		req.GetPlayerId(), req.GetModuleType(), req.GetSubModuleType())

	return rsp, nil
}
