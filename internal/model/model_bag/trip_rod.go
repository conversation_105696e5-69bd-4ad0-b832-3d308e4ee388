package model_bag

import (
	"context"
	"encoding/json"
	modelRodRig "hallsrv/internal/model/model_rod_rig"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
)

const (
	TableTripRod      = "t_trip_rod"
	TableTripRodGroup = "t_trip_rod_group"
)

// 钓组属性
type TTripRod struct {
	PlayerId   uint64          `xorm:"pk comment('用户id')" json:"player_id"`
	Id         int32           `xorm:"pk comment('钓组id')" json:"id"`
	SitId      int32           `xorm:"pk comment('装备位置id')" json:"sit_id"`
	ItemId     int64           `xorm:"comment('道具id')" json:"item_id"`
	InstanceId string          `xorm:"comment('实例id')" json:"instance_id"`
	UpdateAt   int64           `xorm:"updated"`
	Args       map[int32]int64 `xorm:"comment('参数')" json:"args"`
}

func (t *TTripRod) TableName() string {
	return TableTripRod
}

func (t *TTripRod) IsSet() bool {
	return t.InstanceId != ""
}

// // 从道具属性赋值
func (t *TTripRod) FromItem(item *commonPB.Item) {
	t.InstanceId = item.InstanceId
	t.ItemId = item.ItemId
	t.Args = item.Extra
}

func (t *TTripRod) ToItem(ctx context.Context) *commonPB.Item {
	if t == nil {
		return nil
	}
	entry := logx.NewLogEntry(ctx)
	cfg := cmodel.GetItem(t.ItemId, consul_config.WithGrpcCtx(ctx))
	if cfg == nil {
		entry.Debugf("item not found: %d", t.ItemId)
		return nil
	}
	item := &commonPB.Item{
		ItemId:       t.ItemId,
		ItemCategory: commonPB.ITEM_CATEGORY(cfg.CategoryId),
		ItemType:     commonPB.ITEM_TYPE(cfg.ItemType),
		InstanceId:   t.InstanceId,
		Extra:        t.Args,
	}
	return item
}

func (t *TTripRod) ToItemBase(ctx context.Context) *commonPB.ItemBase {
	item := &commonPB.ItemBase{
		ItemId:     t.ItemId,
		InstanceId: t.InstanceId,
		ItemCount:  1,
	}
	return item
}

type TTripRodGroup struct {
	Id       int32               `xorm:"autoincr comment('钓组id')" json:"id"`
	PlayerId uint64              `xorm:"comment('用户id')" json:"player_id"`
	Data     map[int32]*TTripRod `xorm:"extends" json:"data"`
	Name     string              `xorm:"comment('钓组名称')" json:"name"`
	BagIndex int32               `xorm:"comment('背包位置')" json:"bag_index"`
	UpdateAt int64               `xorm:"updated"`
	Check    int32               `xorm:"comment('检查状态')" json:"check"`
}

func (t *TTripRodGroup) TableName() string {
	return TableTripRodGroup
}

// AfterLoad Xorm回调事件特性 大批量查询估计有性能问题
func (t *TTripRodGroup) AfterLoad(session *xorm.Session) {
	list := make([]*TTripRod, 0)
	query := &TTripRod{PlayerId: t.PlayerId, Id: t.Id}
	err := session.Find(&list, query)
	if err != nil {
		logrus.Warnf("load TTripRod fail:%v query%v", err, query)
		return
	}
	t.Data = make(map[int32]*TTripRod)
	for _, one := range list {
		t.Data[one.SitId] = one
	}
}

func (t *TTripRodGroup) FromHash(str string) error {
	err := json.Unmarshal([]byte(str), t)
	if err != nil {
		return err
	}
	return nil
}

func (t *TTripRodGroup) ToHash() []byte {
	js, _ := json.Marshal(t)
	return js
}

func (t *TTripRodGroup) ToProto(ctx context.Context) *commonPB.RodBagInfo {
	bagInfo := &commonPB.RodBagInfo{
		Id:       t.Id,
		Info:     make(map[int32]*commonPB.Item),
		Name:     t.Name,
		BagIndex: t.BagIndex,
	}
	for _, rod := range t.Data {
		if rod.ItemId == 0 || rod.ItemId == -1 {
			continue
		}
		bagInfo.Info[rod.SitId] = rod.ToItem(ctx)
	}
	return bagInfo
}

func (t *TTripRodGroup) SetRod(sitId int32, itemId int64) {
	t.Data[sitId] = &TTripRod{
		PlayerId: t.PlayerId,
		Id:       t.Id,
		SitId:    sitId,
		ItemId:   itemId,
	}
}
func (t *TTripRodGroup) SetRodByItem(sitId int32, item *commonPB.Item) {
	if item == nil {
		delete(t.Data, sitId)
		return
	}
	t.Data[sitId] = &TTripRod{
		PlayerId:   t.PlayerId,
		Id:         t.Id,
		SitId:      sitId,
		ItemId:     item.ItemId,
		InstanceId: item.InstanceId,
		Args:       item.Extra,
	}
}

func (t *TTripRodGroup) FromRigInfo(info *modelRodRig.TRodRigInfo) {
	t.Name = info.Name
	t.PlayerId = info.PlayerId
	t.Data = make(map[int32]*TTripRod)
	t.SetRod(int32(commonPB.TRIP_ROD_SIT_TRS_ROD), info.RodId)
	t.SetRod(int32(commonPB.TRIP_ROD_SIT_TRS_REEL), info.ReelId)
	t.SetRod(int32(commonPB.TRIP_ROD_SIT_TRS_LINE), info.LineId)
	t.SetRod(int32(commonPB.TRIP_ROD_SIT_TRS_LEADER), info.LeaderId)
	//t.SetRod(int32(commonPB.TRIP_ROD_SIT_TRS_BAIT), info.BaitsId)
	//t.SetRod(int32(commonPB.TRIP_ROD_SIT_TRS_HOOKS), info.HooksId)
	t.SetRod(int32(commonPB.TRIP_ROD_SIT_TRS_BOBBER), info.FloatsId)
}

func (t *TTripRodGroup) ToRodList() []*TTripRod {
	list := make([]*TTripRod, 0)
	for _, rod := range t.Data {
		list = append(list, rod)
	}
	return list
}

func (t *TTripRodGroup) GetSubItemList(ctx context.Context) []int32 {
	if t.Data == nil {
		return nil
	}
	itemConf := cmodel.GetAllItem(consul_config.WithGrpcCtx(ctx))
	if itemConf == nil {
		return nil
	}
	typlist := make([]int32, 0)
	typlist = append(typlist, getItemSubType(itemConf, t.Data, int32(commonPB.TRIP_ROD_SIT_TRS_ROD)))
	typlist = append(typlist, getItemSubType(itemConf, t.Data, int32(commonPB.TRIP_ROD_SIT_TRS_REEL)))
	typlist = append(typlist, getItemSubType(itemConf, t.Data, int32(commonPB.TRIP_ROD_SIT_TRS_LINE)))
	typlist = append(typlist, getItemSubType(itemConf, t.Data, int32(commonPB.TRIP_ROD_SIT_TRS_LEADER)))
	typlist = append(typlist, getItemSubType(itemConf, t.Data, int32(commonPB.TRIP_ROD_SIT_TRS_BOBBER)))
	typlist = append(typlist, getItemSubType(itemConf, t.Data, int32(commonPB.TRIP_ROD_SIT_TRS_BAIT)))
	typlist = append(typlist, getItemSubType(itemConf, t.Data, int32(commonPB.TRIP_ROD_SIT_TRS_HOOKS)))

	return typlist
}

// getItemSubType 查询道具子类型
func getItemSubType(itemConfMap map[int64]*cmodel.Item, rodItemList map[int32]*TTripRod, sitId int32) int32 {
	item := rodItemList[sitId]
	if item == nil {
		return 0
	}
	itemConf := itemConfMap[item.ItemId]
	if itemConf == nil {
		return 0
	}
	// 过滤损坏道具
	if item.InstanceId == "" {
		return 0
	}

	return itemConf.SubType
}
