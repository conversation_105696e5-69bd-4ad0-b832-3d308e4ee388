package modelPond

import (
	"encoding/json"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
	"strings"
)

type LastGameInfo struct {
	PlayerId uint64 `json:"player_id"` // 玩家ID
	PondId   int64  `json:"pond_id"`   // 钓场id
	SpotId   int32  `json:"spot_id"`   // 钓点id
	RoomType int32  `json:"room_type"` // 房间类型
	GameTime int64  `json:"game_time"` // 游戏时间
}

// NewLastGameInfo 初始化上次游戏信息
func NewLastGameInfo(playerId uint64, pondId int64, roomType int32, spotId int32) *LastGameInfo {
	return &LastGameInfo{
		PlayerId: playerId,
		PondId:   pondId,
		SpotId:   spotId,
		RoomType: roomType,
		GameTime: timex.Now().Unix(),
	}
}

// NewLastGameInfoFromJsonStr 根据json str 初始化
func NewLastGameInfoFromRdsHash(hash map[string]string) *LastGameInfo {
	lastGameInfo := &LastGameInfo{}

	// 遍历哈希并填充结构体字段
	for key, value := range hash {
		switch strings.ToLower(key) {
		case "player_id":
			lastGameInfo.PlayerId = transform.Str2Uint64(value)
		case "pond_id":
			lastGameInfo.PondId = transform.Str2Int64(value)
		case "spot_id":
			lastGameInfo.SpotId = transform.Str2Int32(value)
		case "room_type":
			lastGameInfo.RoomType = transform.Str2Int32(value)
		case "game_time":
			lastGameInfo.GameTime = transform.Str2Int64(value)
		default:
			logrus.Warnf("filed:%s, not find", key)
		}
	}

	return lastGameInfo
}

// TOJsonStr 转json字符串
func (l *LastGameInfo) ToJsonStr() string {
	if l == nil {
		return ""
	}

	// 序列化成字符串
	jsonByte, err := json.Marshal(l)
	if err != nil {
		return ""
	}
	return string(jsonByte)
}

// ToProto 转化为proto
func (l *LastGameInfo) ToProto() *commonPB.RoomInfo {
	if l == nil {
		return nil
	}

	return &commonPB.RoomInfo{
		PondId:   l.PondId,
		RoomType: commonPB.ROOM_TYPE(l.RoomType),
		SpotId:   l.SpotId,
	}
}

// ToRedisHashField 转化为redis hash field
func (l *LastGameInfo) ToRedisHashField() map[string]interface{} {
	if l == nil {
		return nil
	}

	return map[string]interface{}{
		"player_id": l.PlayerId,
		"pond_id":   l.PondId,
		"spot_id":   l.SpotId,
		"room_type": l.RoomType,
		"game_time": l.GameTime,
	}
}
