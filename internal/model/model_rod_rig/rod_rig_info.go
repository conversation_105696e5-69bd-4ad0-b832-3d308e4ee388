package modelRodRig

import (
	"context"
	"encoding/json"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// TRodRigInfo 竿组信息
type TRodRigInfo struct {
	PlayerId uint64 `xorm:"pk not null comment('玩家ID') BIGINT(20)"`
	Name     string `xorm:"not null comment('名称') VARCHAR(256)"`
	RigId    int32  `xorm:"pk not null comment('钓组id') INT(10)"`
	RodId    int64  `xorm:"not null comment('鱼竿杆ID') BIGINT(20)"`
	ReelId   int64  `xorm:"not null comment('鱼轮ID') BIGINT(20)"`
	LineId   int64  `xorm:"not null comment('主线ID') BIGINT(20)"`
	LeaderId int64  `xorm:"not null comment('子线ID') BIGINT(20)"`
	BaitsId  int64  `xorm:"not null comment('鱼饵ID') BIGINT(20)"`
	HooksId  int64  `xorm:"not null comment('鱼钩ID') BIGINT(20)"`
	FloatsId int64  `xorm:"not null comment('浮标ID') BIGINT(20)"`
	ExtInfo  string `xorm:"default null comment('扩展信息') VARCHAR(256)"`

	DeletedAt  time.Time `xorm:"default null comment('删除时间') DATETIME deleted"`
	CreateTime time.Time `xorm:"not null comment('创建时间') DATETIME created"`
	UpdateTime time.Time `xorm:"not null comment('更新时间') DATETIME updated"`
}

// NewRodRigInfoFromJson 初始化竿组信息
func NewRodRigInfoFromJson(jsonStr string) *TRodRigInfo {
	if jsonStr == "" {
		return nil
	}

	rodRigInfo := &TRodRigInfo{}
	if err := json.Unmarshal([]byte(jsonStr), rodRigInfo); err != nil {
		return nil
	}

	return rodRigInfo
}

func NewRodRigInfoFromProto(playerId uint64, proto *commonPB.RodRigInfo) *TRodRigInfo {
	if proto == nil || playerId <= 0 {
		return nil
	}

	return &TRodRigInfo{
		PlayerId: playerId,
		RigId:    int32(proto.GetRigId()),
		Name:     proto.GetName(),
		RodId:    proto.GetRodId(),
		ReelId:   proto.GetReelId(),
		LineId:   proto.GetLineId(),
		LeaderId: proto.GetLeaderId(),
		BaitsId:  proto.GetBaitId(),
		HooksId:  proto.GetHookId(),
		FloatsId: proto.GetFloatId(),
	}
}

func (r *TRodRigInfo) ToJsonStr() string {
	jsonStr, _ := json.Marshal(r)
	return string(jsonStr)
}

func (r *TRodRigInfo) String() string {
	return r.ToJsonStr()
}

func (r *TRodRigInfo) ToProto() *commonPB.RodRigInfo {
	if r == nil {
		return nil
	}

	return &commonPB.RodRigInfo{
		Name:     r.Name,
		RigId:    r.RigId,
		RodId:    r.RodId,
		ReelId:   r.ReelId,
		LineId:   r.LineId,
		LeaderId: r.LeaderId,
		BaitId:   r.BaitsId,
		HookId:   r.HooksId,
		FloatId:  r.FloatsId,
	}
}

func (r *TRodRigInfo) GetSubItemList(ctx context.Context) []int32 {
	if r == nil {
		return nil
	}

	itemConf := cmodel.GetAllItem(consul_config.WithGrpcCtx(ctx))
	if itemConf == nil {
		return nil
	}

	typeList := make([]int32, 0)
	typeList = append(typeList, getItemSubType(itemConf[r.RodId]))
	typeList = append(typeList, getItemSubType(itemConf[r.ReelId]))
	typeList = append(typeList, getItemSubType(itemConf[r.LineId]))
	typeList = append(typeList, getItemSubType(itemConf[r.LeaderId]))
	typeList = append(typeList, getItemSubType(itemConf[r.FloatsId]))

	return typeList
}

// getItemSubType 查询道具子类型
func getItemSubType(itemConf *cmodel.Item) int32 {
	// 过滤损坏道具

	if itemConf == nil {
		return 0
	}

	return itemConf.SubType
}
