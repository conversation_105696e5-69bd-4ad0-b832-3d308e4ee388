package modelItem

import (
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"math/rand"
	"strings"
	"sync"
	"testing"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"

	"github.com/stretchr/testify/assert"
)

var (
	cacheMap sync.Map
)

func TestItemBase2JsonStr(t *testing.T) {

	for i := 0; i < 200000; i++ {
		cacheMap.Store(i, []int{i})
	}

	for i := 0; i < 100; i++ {
		// 随机取一个 key的数据 测试消耗时间
		randKey := rand.Intn(200000)
		start := timex.Now()

		value, _ := cacheMap.Load(randKey)
		elapsed := time.Since(start)
		t.Logf("value: %v, elapsed:%+v", value, elapsed)
		fmt.Printf("randKey: %v, value: %+v\n", randKey, value)
	}

	match := strings.HasPrefix("1|2|3", "1|2|3")

	t.Logf("match: %v", match)
	item1 := &commonPB.ItemBase{
		ItemId:    1,
		ItemCount: 10,
	}
	item2 := &commonPB.ItemBase{
		ItemId:    2,
		ItemCount: 20,
	}
	str := ItemBase2JsonStr([]*commonPB.ItemBase{item1, item2})
	assert.NotEmpty(t, str)

	str = ItemBase2JsonStr([]*commonPB.ItemBase{item1, item2})
	assert.NotEmpty(t, str)
}
