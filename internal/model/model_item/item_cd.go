package modelItem

import (
	"encoding/json"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// 玩家道具冷却
type TItemCd struct {
	PlayerId    uint64 `json:"player_id"`     // 玩家ID
	ItemId      int64  `json:"item_id"`       // 道具ID
	LastUseTime int64  `json:"last_use_time"` // 最后使用时间戳
}

func NewItemCdFromJsonStr(jsonStr string) *TItemCd {
	itemCd := &TItemCd{}
	err := json.Unmarshal([]byte(jsonStr), itemCd)
	if err != nil {
		return nil
	}

	return itemCd
}

func (t *TItemCd) ToJsonStr() string {
	jsonBytes, err := json.Marshal(t)
	if err != nil {
		return ""
	}

	return string(jsonBytes)
}

func (t *TItemCd) ToProto() *commonPB.ItemCdInfo {
	return &commonPB.ItemCdInfo{
		ItemId:      t.ItemId,
		LastUseTime: t.LastUseTime,
	}
}
