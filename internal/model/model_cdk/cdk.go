package model_cdk

import (
	"encoding/json"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/lib/algorithm/alg_bitmap"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"time"
)

// CDK批次状态常量
const (
	CDKStatusValid   = 1 // 有效
	CDKStatusInvalid = 2 // 已作废
)

// CDKBatch corresponds to the t_cdk table, representing a batch of CDKs.
type CDKBatch struct {
	ID               uint64    `xorm:"pk autoincr 'id'"`      // 自增主键
	ChannelID        int32     `xorm:"'channel_id'"`          // 渠道ID
	Description      string    `xorm:"'description'"`         // 批次描述
	GenerationOption int32     `xorm:"'generation_option'"`   // CDK生成方式 1: 随机生成 2: 手动输入
	StartTime        time.Time `xorm:"'start_time'"`          // 生效开始时间
	EndTime          time.Time `xorm:"'end_time'"`            // 生效结束时间
	CDKCount         int32     `xorm:"'cdk_count'"`           // cdk数量 (此批次生成的CDK总数)
	CDKLimit         int32     `xorm:"default 0 'cdk_limit'"` // 单个cdk可被所有用户使用的总次数（0表示不限制）
	Rewards          string    `xorm:"TEXT 'rewards'"`        // 奖励内容（JSON格式：[{"item_id": 1, "count": 5}]）
	Status           int32     `xorm:"'status'"`              // 状态（CDKStatusValid=有效，CDKStatusInvalid=已作废）
	CreatedAt        time.Time `xorm:"created 'created_at'"`  // 创建时间
	UpdatedAt        time.Time `xorm:"updated 'updated_at'"`  // 更新时间
}

func (c *CDKBatch) TableName() string {
	return "t_cdk_batch"
}

// CDKRecord corresponds to the t_cdk_record table, representing a specific CDK code and its usage.
type CDKRecord struct {
	ID        uint64    `xorm:"pk autoincr 'id'"`                 // 自增主键
	BatchID   uint64    `xorm:"index 'batch_id'"`                 // 批次id 关联t_cdk.id
	CDK       string    `xorm:"varchar(30) notnull unique 'cdk'"` // CDK码（12位字符）
	UsedBM    string    `xorm:"TEXT 'used_bm'"`                   // 兑换账号bitmap
	UsedCount int32     `xorm:"default 0 'used_count'"`           // 已使用次数
	CreatedAt time.Time `xorm:"created 'created_at'"`             // 创建时间
	UpdatedAt time.Time `xorm:"updated 'updated_at'"`             // 兑换时间
}

func (c *CDKRecord) TableName() string {
	return "t_cdk_record"
}

// ToCDKRecordInfo 转换为记录信息
func (c *CDKRecord) ToCDKRecordInfo() *CDKRecordInfo {
	if c == nil {
		return nil
	}
	return &CDKRecordInfo{
		ID:        c.ID,
		CDK:       c.CDK,
		UsedCount: c.UsedCount,
		CreatedAt: c.CreatedAt,
		UpdatedAt: c.UpdatedAt,
	}
}

// CdkBatchRecordResult 结构用于映射从 t_cdk_batch 和 t_cdk_record 表连接查询的结果
type CdkBatchRecordResult struct {
	ChannelID   string    `xorm:"channel_id"` // 来自 t_cdk_batch.channel_id
	Cdk         string    `xorm:"cdk"`        // 来自 t_cdk_record.cdk
	StartTime   time.Time `xorm:"start_time"` // 来自 t_cdk_batch.start_time
	EndTime     time.Time `xorm:"end_time"`   // 来自 t_cdk_batch.end_time
	CdkLimit    int       `xorm:"cdk_limit"`  // 来自 t_cdk_batch.cdk_limit (批次CDK上限)
	UsedCount   int       `xorm:"used_count"` // 来自 t_cdk_batch.used_count (批次已使用数量)
	Rewards     string    `xorm:"rewards"`    // 来自 t_cdk_batch.rewards (奖励内容，可能是JSON字符串)
	Status      int       `xorm:"status"`     // 来自 t_cdk_batch.status (批次状态，CDKStatusValid=有效，CDKStatusInvalid=已作废)
	UsedBm      string    `xorm:"used_bm"`    // 来自 t_cdk_record.used_bm (兑换账号bitmap)
	DoubleCheck bool      `xorm:"-"`          // 用于双重检查的标志位
}

// GetRewards 解析奖励内容
func (c *CdkBatchRecordResult) GetRewards() ([]*commonPB.ItemBase, error) {
	rewards := make([]*commonPB.ItemBase, 0)
	err := json.Unmarshal([]byte(c.Rewards), &rewards)
	return rewards, err
}

// ToJSON 序列化为JSON字符串，用于缓存
func (c *CdkBatchRecordResult) ToJSON() (string, error) {
	if c == nil {
		return "", nil
	}
	data, err := json.Marshal(c)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串反序列化
func (c *CdkBatchRecordResult) FromJSON(jsonStr string) error {
	if jsonStr == "" {
		return nil
	}
	return json.Unmarshal([]byte(jsonStr), c)
}

// NewCdkBatchRecordResultFromJSON 从JSON字符串创建实例
func NewCdkBatchRecordResultFromJSON(jsonStr string) (*CdkBatchRecordResult, error) {
	if jsonStr == "" {
		return nil, nil
	}
	result := &CdkBatchRecordResult{}
	err := json.Unmarshal([]byte(jsonStr), result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// CreateInvalidCdkResult 创建无效CDK结果
func CreateInvalidCdkResult(cdkCode string) *CdkBatchRecordResult {
	return &CdkBatchRecordResult{
		Cdk:    cdkCode,
		Status: CDKStatusInvalid,
	}
}

// ValidateCdk 校验CDK的有效性
func ValidateCdk(dbRes *CdkBatchRecordResult, playerID uint64) error {
	if dbRes == nil {
		return protox.CodeError(commonPB.ErrCode_ERR_HALL_CDKEY_EXCHANGE_FAILED, "cdk data is nil")
	}

	// 检查CDK批次状态
	if dbRes.Status != CDKStatusValid {
		return protox.CodeError(commonPB.ErrCode_ERR_HALL_CDKEY_EXCHANGE_FAILED, "cdk batch is invalid or disabled")
	}

	// CDK码自身的使用次数限制
	if dbRes.CdkLimit > 0 && dbRes.UsedCount >= dbRes.CdkLimit {
		return protox.CodeError(commonPB.ErrCode_ERR_HALL_CDKEY_EXCHANGE_MAX, "cdk usage limit exceeded")
	}

	// 检查时间有效性
	now := timex.Now()
	if now.Before(dbRes.StartTime) {
		return protox.CodeError(commonPB.ErrCode_ERR_HALL_CDKEY_EXCHANGE_EXPIRED, "cdk not yet active")
	}
	if now.After(dbRes.EndTime) {
		return protox.CodeError(commonPB.ErrCode_ERR_HALL_CDKEY_EXCHANGE_EXPIRED, "cdk has expired")
	}

	// 玩家是否已使用过此CDK码
	if dbRes.UsedBm != "" {
		if alg_bitmap.InBitmap(dbRes.UsedBm, playerID) {
			return protox.CodeError(commonPB.ErrCode_ERR_HALL_CDKEY_EXCHANGE_USED, "player has already used this cdk")
		}
	}

	return nil
}

// BatchToCDKRecordInfos 批量转换为记录信息
func BatchToCDKRecordInfos(records []*CDKRecord) []*CDKRecordInfo {
	if len(records) == 0 {
		return nil
	}

	recordInfos := make([]*CDKRecordInfo, 0, len(records))
	for _, record := range records {
		if recordInfo := record.ToCDKRecordInfo(); recordInfo != nil {
			recordInfos = append(recordInfos, recordInfo)
		}
	}
	return recordInfos
}
