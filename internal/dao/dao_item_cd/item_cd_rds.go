package daoItemCd

import (
	"context"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"hallsrv/internal/config"
	modelItem "hallsrv/internal/model/model_item"
)

// GetPlayerItemCdRds 查询玩家道具冷却信息
func GetPlayerItemCdRds(ctx context.Context, playerId uint64) (map[int64]*modelItem.TItemCd, error) {
	entry := logx.NewLogEntry(ctx)

	key := config.ItemCooldownKey(playerId)
	cdArr, err := redisx.GetGeneralCli().HGetAllWithNil(ctx, key).Result()
	if err != nil {
		entry.Warnf("query player:%d item cd warn:%+v", playerId, err)
		return nil, err
	}

	// 解析出数据
	cdMap := make(map[int64]*modelItem.TItemCd, len(cdArr))
	for _, v := range cdArr {
		cdInfo := modelItem.NewItemCdFromJsonStr(v)
		if cdInfo == nil {
			continue
		}

		cdMap[cdInfo.ItemId] = cdInfo
	}

	return cdMap, nil
}

// UpdatePlayerItemCdRds 更新玩家道具冷却信息
func UpdatePlayerItemCdRds(ctx context.Context, playerId uint64, cdInfo []*modelItem.TItemCd) error {
	entry := logx.NewLogEntry(ctx)

	key := config.ItemCooldownKey(playerId)
	pipline := redisx.GetGeneralCli().TxPipeline()
	rdsHashArr := make([]interface{}, 0, len(cdInfo)*2)

	for _, v := range cdInfo {
		rdsHashArr = append(rdsHashArr, v.ItemId)
		rdsHashArr = append(rdsHashArr, v.ToJsonStr())
	}

	pipline.HMSet(ctx, key, rdsHashArr...)
	pipline.Expire(ctx, key, config.ITEM_COOLDOWN_EXPIRE)

	_, err := pipline.Exec(ctx)
	if err != nil {
		entry.Errorf("update player:%d item cd error:%+v", playerId, err)
		return err
	}

	return nil
}
