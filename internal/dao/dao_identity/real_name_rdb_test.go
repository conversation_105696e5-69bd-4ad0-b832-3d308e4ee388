package dao_identity

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/mysqlx"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	model "hallsrv/internal/model/model_identity"
	"testing"
)

func init() {
	mysqlx.InitMysqlGeneralDbDevEnv()
}

func TestSyncTable(t *testing.T) {
	SyncRealNameAuthTable()
}

func TestInsert(t *testing.T) {
	err := Insert(&model.TPlayerRealNameAuth{
		ProductId:  1,
		PlayerId:   1,
		Pi:         "test_pi",
		RealName:   "test_name",
		IdCardNum:  "123456199011123456",
		Year:       1990,
		Month:      11,
		Day:        12,
		CreateTime: timex.Now(),
	},
	)

	if err != nil {
		t.Log(err)
	}
}

func TestLoad(t *testing.T) {
	exist, err := Load(2, 1)
	if err != nil {
		t.Log(err)
	}
	t.Log(exist)
}
